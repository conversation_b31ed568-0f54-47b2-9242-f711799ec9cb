"""
Simplified FastAPI application for Reality 2.0 RAG Server
This version includes basic functionality without complex dependencies.
"""

import os
import time
import logging
from pathlib import Path
from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NResponse
from typing import Optional, List
import uvicorn

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Reality 2.0",
    description="High-End Web Application",
    version="1.0.0",
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Create necessary directories
upload_dir = Path("./uploads")
upload_dir.mkdir(exist_ok=True)

# In-memory storage for demo (replace with database later)
documents = []
document_counter = 0

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with application information."""
    return {
        "message": "Welcome to Reality 2.0",
        "version": "1.0.0",
        "description": "High-End Web Application",
        "docs_url": "/docs",
        "health_url": "/health",
        "features": [
            "Document Management",
            "File Upload",
            "API Documentation",
            "Health Monitoring"
        ]
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "app_name": "Reality 2.0 RAG Server",
        "version": "1.0.0",
        "environment": "development",
        "timestamp": time.time(),
        "components": {
            "api": "healthy",
            "storage": "healthy",
            "upload_dir": str(upload_dir.exists())
        }
    }

# API v1 health check
@app.get("/api/v1/health")
async def api_health_check():
    """API v1 health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "app_name": "Reality 2.0 RAG Server",
        "version": "1.0.0",
        "environment": "development",
        "endpoints": {
            "documents": "/api/v1/documents",
            "rag": "/api/v1/rag",
            "health": "/api/v1/health"
        }
    }

# Document upload endpoint
@app.post("/api/v1/documents/upload")
async def upload_document(
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    author: Optional[str] = Form(None)
):
    """
    Upload a document for processing.
    """
    global document_counter
    
    try:
        # Validate file type
        allowed_types = ['.txt', '.md', '.pdf', '.docx']
        file_extension = Path(file.filename).suffix.lower()
        
        if file_extension not in allowed_types:
            raise HTTPException(
                status_code=400, 
                detail=f"Unsupported file type. Allowed: {', '.join(allowed_types)}"
            )
        
        # Read file content
        content = await file.read()
        
        # Validate file size (10MB limit)
        if len(content) > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File too large (max 10MB)")
        
        # Save file
        document_counter += 1
        filename = f"doc_{document_counter}_{file.filename}"
        file_path = upload_dir / filename
        
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Create document record
        document = {
            "id": document_counter,
            "filename": filename,
            "original_filename": file.filename,
            "title": title or file.filename,
            "description": description,
            "author": author,
            "file_size": len(content),
            "file_type": file_extension[1:],
            "upload_time": time.time(),
            "status": "uploaded",
            "file_path": str(file_path)
        }
        
        documents.append(document)
        
        logger.info(f"Uploaded document: {file.filename} (ID: {document_counter})")
        
        return {
            "message": "Document uploaded successfully",
            "document_id": document_counter,
            "filename": filename,
            "original_filename": file.filename,
            "file_size": len(content),
            "status": "uploaded"
        }
        
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# List documents endpoint
@app.get("/api/v1/documents")
async def list_documents():
    """
    List all uploaded documents.
    """
    return {
        "documents": documents,
        "total": len(documents),
        "message": f"Found {len(documents)} documents"
    }

# Get document by ID
@app.get("/api/v1/documents/{document_id}")
async def get_document(document_id: int):
    """
    Get document details by ID.
    """
    document = next((doc for doc in documents if doc["id"] == document_id), None)
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return document

# Simple search endpoint
@app.get("/api/v1/rag/quick-search")
async def quick_search(q: str):
    """
    Simple search through document titles and descriptions.
    """
    if not q:
        raise HTTPException(status_code=400, detail="Query parameter 'q' is required")
    
    # Simple text search in titles and descriptions
    results = []
    for doc in documents:
        score = 0
        query_lower = q.lower()
        
        if query_lower in doc["title"].lower():
            score += 2
        if doc["description"] and query_lower in doc["description"].lower():
            score += 1
        if query_lower in doc["original_filename"].lower():
            score += 1
        
        if score > 0:
            results.append({
                "document": doc,
                "score": score,
                "matches": score
            })
    
    # Sort by score
    results.sort(key=lambda x: x["score"], reverse=True)
    
    return {
        "query": q,
        "results": results[:10],  # Top 10 results
        "total": len(results),
        "message": f"Found {len(results)} matching documents"
    }

# Simple Q&A endpoint
@app.get("/api/v1/rag/quick-generate")
async def quick_generate(q: str):
    """
    Simple Q&A endpoint (placeholder for now).
    """
    if not q:
        raise HTTPException(status_code=400, detail="Query parameter 'q' is required")
    
    # For now, return a simple response
    # This would be replaced with actual RAG functionality
    return {
        "question": q,
        "answer": f"This is a placeholder response for: '{q}'. The RAG system would analyze your uploaded documents and provide a comprehensive answer based on the content.",
        "sources": len(documents),
        "note": "Full RAG functionality will be available once all dependencies are installed."
    }

# System status endpoint
@app.get("/api/v1/rag/status")
async def rag_status():
    """
    Get RAG system status.
    """
    return {
        "status": "operational",
        "version": "1.0.0",
        "features": {
            "document_upload": True,
            "simple_search": True,
            "basic_qa": True,
            "advanced_rag": False,  # Will be True when full system is ready
            "embeddings": False,
            "vector_search": False
        },
        "statistics": {
            "total_documents": len(documents),
            "upload_directory": str(upload_dir),
            "supported_formats": ["txt", "md", "pdf", "docx"]
        },
        "next_steps": [
            "Install additional dependencies for full RAG functionality",
            "Configure embedding models",
            "Set up vector database"
        ]
    }

# Delete document endpoint
@app.delete("/api/v1/documents/{document_id}")
async def delete_document(document_id: int):
    """
    Delete a document by ID.
    """
    global documents
    
    document = next((doc for doc in documents if doc["id"] == document_id), None)
    
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Remove file
    try:
        file_path = Path(document["file_path"])
        if file_path.exists():
            file_path.unlink()
    except Exception as e:
        logger.warning(f"Could not delete file: {e}")
    
    # Remove from list
    documents = [doc for doc in documents if doc["id"] != document_id]
    
    return {"message": f"Document {document_id} deleted successfully"}

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}")
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

if __name__ == "__main__":
    print("🚀 Starting Reality 2.0 RAG Server...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("📁 Upload Documents: http://localhost:8000/api/v1/documents/upload")
    
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
