"""
Document management API endpoints.
"""

from typing import List, Optional
from pathlib import Path
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import logging

from core.database import get_db
from core.config import settings
from schemas.document import (
    DocumentResponse, DocumentCreate, DocumentUpdate, DocumentListResponse,
    DocumentStats, FileUploadResponse, BulkUploadResponse, DocumentFilters,
    PaginationParams, DocumentProcessingStatus
)
from services.document_service import DocumentService
from crud import document as document_crud

logger = logging.getLogger(__name__)

router = APIRouter()
document_service = DocumentService()


@router.post("/upload", response_model=FileUploadResponse)
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: Optional[str] = Form(None),
    description: Optional[str] = Form(None),
    author: Optional[str] = Form(None),
    language: str = Form("en"),
    tags: Optional[str] = Form(None),
    db: Session = Depends(get_db)
):
    """
    Upload a new document.
    
    Args:
        file: Uploaded file
        title: Optional document title
        description: Optional document description
        author: Optional document author
        language: Document language (default: en)
        tags: Optional comma-separated tags
        db: Database session
        
    Returns:
        Upload response with document information
    """
    try:
        # Validate file
        if not document_service.file_handler.is_supported_file(file.filename):
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Supported types: {', '.join(settings.allowed_file_types)}"
            )
        
        # Read file content
        file_content = await file.read()
        
        if len(file_content) > settings.max_file_size:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {settings.max_file_size} bytes"
            )
        
        # Prepare document data
        document_data = None
        if any([title, description, author, tags]):
            tag_list = [tag.strip() for tag in tags.split(",")] if tags else None
            document_data = DocumentCreate(
                title=title,
                description=description,
                author=author,
                language=language,
                tags=tag_list
            )
        
        # Create document
        document = await document_service.create_document_from_file(
            db=db,
            file_content=file_content,
            filename=file.filename,
            document_data=document_data
        )
        
        return FileUploadResponse(
            document_id=document.id,
            filename=document.filename,
            original_filename=document.original_filename,
            file_size=document.file_size,
            file_type=document.file_type,
            status=document.processing_status,
            message="Document uploaded successfully and processing started"
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/upload/bulk", response_model=BulkUploadResponse)
async def upload_documents_bulk(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db)
):
    """
    Upload multiple documents at once.
    
    Args:
        files: List of uploaded files
        db: Database session
        
    Returns:
        Bulk upload response with results
    """
    successful_uploads = []
    failed_uploads = []
    
    for file in files:
        try:
            # Read file content
            file_content = await file.read()
            
            # Create document
            document = await document_service.create_document_from_file(
                db=db,
                file_content=file_content,
                filename=file.filename
            )
            
            successful_uploads.append(FileUploadResponse(
                document_id=document.id,
                filename=document.filename,
                original_filename=document.original_filename,
                file_size=document.file_size,
                file_type=document.file_type,
                status=document.processing_status,
                message="Uploaded successfully"
            ))
            
        except Exception as e:
            failed_uploads.append({
                "filename": file.filename,
                "error": str(e)
            })
    
    return BulkUploadResponse(
        successful_uploads=successful_uploads,
        failed_uploads=failed_uploads,
        total_files=len(files),
        successful_count=len(successful_uploads),
        failed_count=len(failed_uploads)
    )


@router.get("/", response_model=DocumentListResponse)
async def get_documents(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    search: Optional[str] = Query(None, description="Search term"),
    file_type: Optional[str] = Query(None, description="Filter by file type"),
    author: Optional[str] = Query(None, description="Filter by author"),
    language: Optional[str] = Query(None, description="Filter by language"),
    is_processed: Optional[bool] = Query(None, description="Filter by processing status"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    db: Session = Depends(get_db)
):
    """
    Get documents with filtering and pagination.
    
    Returns:
        Paginated list of documents
    """
    try:
        # Prepare filters
        filters = DocumentFilters(
            search=search,
            file_type=file_type,
            author=author,
            language=language,
            is_processed=is_processed,
            tags=[tag.strip() for tag in tags.split(",")] if tags else None
        )
        
        # Prepare pagination
        pagination = PaginationParams(
            page=page,
            size=size,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Get documents
        documents, total = document_service.get_documents(db, filters, pagination)
        
        # Calculate pagination info
        pages = (total + size - 1) // size
        
        return DocumentListResponse(
            documents=[DocumentResponse.from_orm(doc) for doc in documents],
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except Exception as e:
        logger.error(f"Error getting documents: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Get document by ID.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        Document details
    """
    document = document_service.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return DocumentResponse.from_orm(document)


@router.put("/{document_id}", response_model=DocumentResponse)
async def update_document(
    document_id: int,
    document_update: DocumentUpdate,
    db: Session = Depends(get_db)
):
    """
    Update document metadata.
    
    Args:
        document_id: Document ID
        document_update: Update data
        db: Database session
        
    Returns:
        Updated document
    """
    document = document_service.update_document(db, document_id, document_update)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return DocumentResponse.from_orm(document)


@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Delete document and all associated data.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        Success message
    """
    success = await document_service.delete_document(db, document_id)
    if not success:
        raise HTTPException(status_code=404, detail="Document not found")
    
    return {"message": "Document deleted successfully"}


@router.get("/{document_id}/download")
async def download_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Download original document file.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        File response
    """
    document = document_service.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    file_path = document.file_path
    if not file_path or not Path(file_path).exists():
        raise HTTPException(status_code=404, detail="File not found")
    
    return FileResponse(
        path=file_path,
        filename=document.original_filename,
        media_type=document.mime_type
    )


@router.get("/{document_id}/content")
async def get_document_content(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Get extracted text content of document.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        Document text content
    """
    document = document_service.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    if not document.is_processed:
        raise HTTPException(status_code=400, detail="Document not yet processed")
    
    return {
        "document_id": document.id,
        "content": document.content,
        "word_count": document.word_count,
        "character_count": document.character_count
    }


@router.get("/{document_id}/chunks")
async def get_document_chunks(
    document_id: int,
    limit: Optional[int] = Query(None, ge=1, description="Limit number of chunks"),
    offset: Optional[int] = Query(None, ge=0, description="Offset for pagination"),
    db: Session = Depends(get_db)
):
    """
    Get chunks for a document.
    
    Args:
        document_id: Document ID
        limit: Optional limit
        offset: Optional offset
        db: Database session
        
    Returns:
        List of document chunks
    """
    document = document_service.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    chunks = document_crud.get_document_chunks(db, document_id, limit, offset)
    
    return {
        "document_id": document_id,
        "chunks": [
            {
                "id": chunk.id,
                "content": chunk.content,
                "chunk_index": chunk.chunk_index,
                "word_count": chunk.word_count,
                "character_count": chunk.character_count,
                "has_embedding": chunk.has_embedding
            }
            for chunk in chunks
        ],
        "total_chunks": document.chunk_count
    }


@router.get("/{document_id}/status", response_model=DocumentProcessingStatus)
async def get_document_processing_status(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    Get document processing status.
    
    Args:
        document_id: Document ID
        db: Database session
        
    Returns:
        Processing status information
    """
    document = document_service.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Calculate progress based on status
    progress = None
    if document.processing_status == "pending":
        progress = 0.0
    elif document.processing_status == "processing":
        progress = 0.5  # Rough estimate
    elif document.processing_status == "completed":
        progress = 1.0
    elif document.processing_status == "failed":
        progress = 0.0
    
    return DocumentProcessingStatus(
        document_id=document.id,
        status=document.processing_status,
        progress=progress,
        message=f"Document is {document.processing_status}",
        error=document.processing_error
    )


@router.post("/{document_id}/reprocess")
async def reprocess_document(
    document_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    Reprocess a document (re-extract text, re-chunk, re-embed).
    
    Args:
        document_id: Document ID
        background_tasks: Background tasks
        db: Database session
        
    Returns:
        Success message
    """
    document = document_service.get_document_by_id(db, document_id)
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Reset processing status
    document.processing_status = "pending"
    document.is_processed = False
    document.processing_error = None
    db.commit()
    
    # Start background processing
    background_tasks.add_task(document_service._process_document_async, document_id)
    
    return {"message": "Document reprocessing started"}


@router.get("/stats/overview", response_model=DocumentStats)
async def get_document_statistics(db: Session = Depends(get_db)):
    """
    Get document statistics and overview.
    
    Args:
        db: Database session
        
    Returns:
        Document statistics
    """
    try:
        stats = document_crud.get_document_statistics(db)
        
        return DocumentStats(
            total_documents=stats['total_documents'],
            processed_documents=stats['processed_documents'],
            pending_documents=stats['pending_documents'],
            failed_documents=stats['failed_documents'],
            total_chunks=stats['total_chunks'],
            total_embeddings=stats['total_embeddings'],
            total_file_size=stats['total_file_size'],
            average_chunk_size=stats['average_chunk_size'],
            supported_file_types=settings.allowed_file_types
        )
        
    except Exception as e:
        logger.error(f"Error getting document statistics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
