"""
RAG (Retrieval-Augmented Generation) API endpoints.
"""

from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
import logging

from core.database import get_db
from schemas.rag import (
    RAGQuery, RAGGenerationQuery, RAGChatQuery,
    RAGRetrievalResponse, RAGGenerationResponse, RAGChatResponse,
    EmbeddingRequest, EmbeddingResponse, SimilaritySearchRequest,
    RAGSystemStatus
)
from services.rag_service import RAGService
from services.embedding_service import EmbeddingService
from services.vector_store_service import VectorStoreService

logger = logging.getLogger(__name__)

router = APIRouter()
rag_service = RAGService()
embedding_service = EmbeddingService()
vector_store_service = VectorStoreService()


@router.post("/search", response_model=RAGRetrievalResponse)
async def search_documents(
    query: RAGQuery,
    db: Session = Depends(get_db)
):
    """
    Search for relevant document chunks using semantic similarity.
    
    Args:
        query: Search query parameters
        db: Database session
        
    Returns:
        Retrieval response with relevant chunks
    """
    try:
        response = await rag_service.retrieve_relevant_chunks(db, query)
        return response
        
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/generate", response_model=RAGGenerationResponse)
async def generate_response(
    query: RAGGenerationQuery,
    db: Session = Depends(get_db)
):
    """
    Generate AI response using retrieved context from documents.
    
    Args:
        query: Generation query parameters
        db: Database session
        
    Returns:
        Generated response with retrieval context
    """
    try:
        response = await rag_service.generate_response(db, query)
        return response
        
    except Exception as e:
        logger.error(f"Error generating response: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/chat", response_model=RAGChatResponse)
async def chat_query(
    query: RAGChatQuery,
    db: Session = Depends(get_db)
):
    """
    Process conversational RAG query with context awareness.
    
    Args:
        query: Chat query parameters
        db: Database session
        
    Returns:
        Chat response with context
    """
    try:
        response = await rag_service.chat_query(db, query)
        return response
        
    except Exception as e:
        logger.error(f"Error processing chat query: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/embeddings", response_model=EmbeddingResponse)
async def generate_embeddings(
    request: EmbeddingRequest
):
    """
    Generate embeddings for text inputs.
    
    Args:
        request: Embedding request with texts
        
    Returns:
        Generated embeddings
    """
    try:
        import time
        start_time = time.time()
        
        embeddings, metadata = await embedding_service.generate_embeddings(
            texts=request.texts,
            model=request.model,
            normalize=request.normalize
        )
        
        processing_time = (time.time() - start_time) * 1000
        
        return EmbeddingResponse(
            embeddings=embeddings,
            model_used=metadata['model_used'],
            dimension=metadata['dimension'],
            processing_time_ms=processing_time
        )
        
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/similarity-search")
async def similarity_search(
    request: SimilaritySearchRequest,
    db: Session = Depends(get_db)
):
    """
    Perform similarity search using a provided embedding vector.
    
    Args:
        request: Similarity search request
        db: Database session
        
    Returns:
        Similar chunks with scores
    """
    try:
        # Search vector store
        similar_chunks = await vector_store_service.search_similar(
            query_embedding=request.query_embedding,
            top_k=request.max_results,
            threshold=request.similarity_threshold
        )
        
        if not similar_chunks:
            return {
                "results": [],
                "total_results": 0
            }
        
        # Get chunk details from database
        from crud.document import get_chunks_by_ids
        chunk_ids = [chunk_id for chunk_id, _ in similar_chunks]
        chunks = get_chunks_by_ids(db, chunk_ids)
        
        # Create response
        results = []
        similarity_scores = {chunk_id: score for chunk_id, score in similar_chunks}
        
        for chunk in chunks:
            similarity_score = similarity_scores.get(chunk.id, 0.0)
            results.append({
                "chunk_id": chunk.id,
                "document_id": chunk.document_id,
                "content": chunk.content,
                "similarity_score": similarity_score,
                "chunk_index": chunk.chunk_index
            })
        
        # Sort by similarity score
        results.sort(key=lambda x: x["similarity_score"], reverse=True)
        
        return {
            "results": results,
            "total_results": len(results)
        }
        
    except Exception as e:
        logger.error(f"Error performing similarity search: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/status", response_model=RAGSystemStatus)
async def get_rag_system_status():
    """
    Get RAG system status and health information.
    
    Returns:
        System status information
    """
    try:
        # Get health checks from all components
        health_check = await rag_service.health_check()
        
        # Get statistics
        vector_stats = vector_store_service.get_stats()
        embedding_info = embedding_service.get_model_info()
        
        # Get document statistics
        from core.database import SessionLocal
        from crud.document import get_document_statistics
        
        db = SessionLocal()
        try:
            doc_stats = get_document_statistics(db)
        finally:
            db.close()
        
        return RAGSystemStatus(
            status=health_check['status'],
            total_documents=doc_stats['total_documents'],
            total_chunks=doc_stats['total_chunks'],
            total_embeddings=doc_stats['total_embeddings'],
            embedding_model=embedding_info['model_name'],
            vector_store_type=vector_stats['store_type'],
            last_updated=datetime.utcnow(),
            health_checks={
                'embedding_service': health_check['embedding_service']['status'] == 'healthy',
                'vector_store': health_check['vector_store']['status'] == 'healthy',
                'llm_available': health_check['llm_available']
            }
        )
        
    except Exception as e:
        logger.error(f"Error getting RAG system status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/models/embedding")
async def get_embedding_model_info():
    """
    Get information about the current embedding model.
    
    Returns:
        Embedding model information
    """
    try:
        info = embedding_service.get_model_info()
        return info
        
    except Exception as e:
        logger.error(f"Error getting embedding model info: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/vector-store/stats")
async def get_vector_store_stats():
    """
    Get vector store statistics.
    
    Returns:
        Vector store statistics
    """
    try:
        stats = vector_store_service.get_stats()
        return stats
        
    except Exception as e:
        logger.error(f"Error getting vector store stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/vector-store/rebuild")
async def rebuild_vector_store():
    """
    Rebuild the vector store index from scratch.
    
    Returns:
        Success message
    """
    try:
        success = await vector_store_service.rebuild_index()
        
        if success:
            return {"message": "Vector store index rebuilt successfully"}
        else:
            raise HTTPException(status_code=500, detail="Failed to rebuild vector store")
            
    except Exception as e:
        logger.error(f"Error rebuilding vector store: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/health")
async def health_check():
    """
    Perform health check on RAG system components.
    
    Returns:
        Health check results
    """
    try:
        health = await rag_service.health_check()
        
        if health['status'] == 'healthy':
            return health
        else:
            raise HTTPException(status_code=503, detail=health)
            
    except Exception as e:
        logger.error(f"Error performing health check: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Quick search endpoint for simple queries
@router.get("/quick-search")
async def quick_search(
    q: str = Query(..., description="Search query"),
    limit: int = Query(5, ge=1, le=20, description="Maximum results"),
    threshold: float = Query(0.7, ge=0.0, le=1.0, description="Similarity threshold"),
    db: Session = Depends(get_db)
):
    """
    Quick search endpoint for simple queries.
    
    Args:
        q: Search query
        limit: Maximum number of results
        threshold: Similarity threshold
        db: Database session
        
    Returns:
        Search results
    """
    try:
        query = RAGQuery(
            query=q,
            max_results=limit,
            similarity_threshold=threshold
        )
        
        response = await rag_service.retrieve_relevant_chunks(db, query)
        
        # Simplified response format
        results = []
        for result in response.results:
            chunk = result.chunk
            results.append({
                "content": chunk.content[:200] + "..." if len(chunk.content) > 200 else chunk.content,
                "document": chunk.document_filename,
                "similarity": chunk.similarity_score,
                "chunk_id": chunk.id
            })
        
        return {
            "query": q,
            "results": results,
            "total": len(results),
            "processing_time_ms": response.processing_time_ms
        }
        
    except Exception as e:
        logger.error(f"Error in quick search: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Quick generation endpoint
@router.get("/quick-generate")
async def quick_generate(
    q: str = Query(..., description="Question to answer"),
    limit: int = Query(3, ge=1, le=10, description="Maximum context chunks"),
    db: Session = Depends(get_db)
):
    """
    Quick generation endpoint for simple Q&A.
    
    Args:
        q: Question to answer
        limit: Maximum context chunks
        db: Database session
        
    Returns:
        Generated answer
    """
    try:
        query = RAGGenerationQuery(
            query=q,
            max_results=limit,
            generate_response=True,
            include_sources=True
        )
        
        response = await rag_service.generate_response(db, query)
        
        return {
            "question": q,
            "answer": response.generated_response.content if response.generated_response else "No answer generated",
            "sources": len(response.retrieval.results),
            "processing_time_ms": response.total_processing_time_ms
        }
        
    except Exception as e:
        logger.error(f"Error in quick generation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
