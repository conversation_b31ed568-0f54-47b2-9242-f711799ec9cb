"""
File handling utilities for document processing and content extraction.
"""

import os
import mimetypes
from pathlib import Path
from typing import Optional, Dict, Any, BinaryIO
import logging
import hashlib
import aiofiles

# Import document processing libraries
try:
    import PyPDF2
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    logging.warning("PyPDF2 not available. PDF processing will be disabled.")

try:
    from docx import Document as DocxDocument
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logging.warning("python-docx not available. DOCX processing will be disabled.")

logger = logging.getLogger(__name__)


class FileHandler:
    """File handling utility class for document processing."""
    
    SUPPORTED_EXTENSIONS = {
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    }
    
    def __init__(self, upload_dir: str = "./uploads"):
        """
        Initialize file handler.
        
        Args:
            upload_dir: Directory for storing uploaded files
        """
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    def is_supported_file(self, filename: str) -> bool:
        """
        Check if file type is supported.
        
        Args:
            filename: Name of the file
            
        Returns:
            True if file type is supported
        """
        extension = Path(filename).suffix.lower()
        return extension in self.SUPPORTED_EXTENSIONS
    
    def get_file_info(self, file_path: Path) -> Dict[str, Any]:
        """
        Get file information and metadata.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary containing file information
        """
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        stat = file_path.stat()
        extension = file_path.suffix.lower()
        
        # Get MIME type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type:
            mime_type = self.SUPPORTED_EXTENSIONS.get(extension, 'application/octet-stream')
        
        return {
            'filename': file_path.name,
            'file_size': stat.st_size,
            'file_type': extension[1:] if extension else 'unknown',
            'mime_type': mime_type,
            'created_at': stat.st_ctime,
            'modified_at': stat.st_mtime,
        }
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> Path:
        """
        Save uploaded file to disk.
        
        Args:
            file_content: File content as bytes
            filename: Original filename
            
        Returns:
            Path to saved file
        """
        # Generate unique filename to avoid conflicts
        file_hash = hashlib.md5(file_content).hexdigest()[:8]
        name_part = Path(filename).stem
        extension = Path(filename).suffix
        unique_filename = f"{name_part}_{file_hash}{extension}"
        
        file_path = self.upload_dir / unique_filename
        
        # Save file asynchronously
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        logger.info(f"Saved file: {file_path}")
        return file_path
    
    def extract_text_content(self, file_path: Path) -> str:
        """
        Extract text content from file based on its type.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Extracted text content
        """
        extension = file_path.suffix.lower()
        
        try:
            if extension == '.txt':
                return self._extract_text_from_txt(file_path)
            elif extension == '.md':
                return self._extract_text_from_markdown(file_path)
            elif extension == '.pdf':
                return self._extract_text_from_pdf(file_path)
            elif extension == '.docx':
                return self._extract_text_from_docx(file_path)
            else:
                raise ValueError(f"Unsupported file type: {extension}")
        except Exception as e:
            logger.error(f"Error extracting text from {file_path}: {e}")
            raise
    
    def _extract_text_from_txt(self, file_path: Path) -> str:
        """Extract text from plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='latin-1') as f:
                return f.read()
    
    def _extract_text_from_markdown(self, file_path: Path) -> str:
        """Extract text from Markdown file."""
        # For now, treat as plain text
        # Could be enhanced to parse Markdown and extract structured content
        return self._extract_text_from_txt(file_path)
    
    def _extract_text_from_pdf(self, file_path: Path) -> str:
        """Extract text from PDF file."""
        if not PDF_AVAILABLE:
            raise RuntimeError("PDF processing not available. Install PyPDF2.")
        
        text_content = []
        
        try:
            with open(file_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content.append(page_text)
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num + 1}: {e}")
                        continue
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {e}")
            raise
        
        return '\n\n'.join(text_content)
    
    def _extract_text_from_docx(self, file_path: Path) -> str:
        """Extract text from DOCX file."""
        if not DOCX_AVAILABLE:
            raise RuntimeError("DOCX processing not available. Install python-docx.")
        
        try:
            doc = DocxDocument(file_path)
            text_content = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            return '\n\n'.join(text_content)
        except Exception as e:
            logger.error(f"Error reading DOCX file {file_path}: {e}")
            raise
    
    def calculate_file_hash(self, file_path: Path) -> str:
        """
        Calculate SHA-256 hash of file content.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Hexadecimal hash string
        """
        hash_sha256 = hashlib.sha256()
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        
        return hash_sha256.hexdigest()
    
    def validate_file_size(self, file_size: int, max_size: int) -> bool:
        """
        Validate file size against maximum allowed size.
        
        Args:
            file_size: Size of the file in bytes
            max_size: Maximum allowed size in bytes
            
        Returns:
            True if file size is valid
        """
        return file_size <= max_size
    
    def get_file_extension(self, filename: str) -> str:
        """
        Get file extension from filename.
        
        Args:
            filename: Name of the file
            
        Returns:
            File extension (without dot)
        """
        return Path(filename).suffix.lower()[1:]  # Remove the dot
    
    async def delete_file(self, file_path: Path) -> bool:
        """
        Delete file from disk.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if file was deleted successfully
        """
        try:
            if file_path.exists():
                file_path.unlink()
                logger.info(f"Deleted file: {file_path}")
                return True
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                return False
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")
            return False
    
    def cleanup_old_files(self, days_old: int = 30) -> int:
        """
        Clean up old files from upload directory.
        
        Args:
            days_old: Delete files older than this many days
            
        Returns:
            Number of files deleted
        """
        import time
        
        current_time = time.time()
        cutoff_time = current_time - (days_old * 24 * 60 * 60)
        deleted_count = 0
        
        try:
            for file_path in self.upload_dir.iterdir():
                if file_path.is_file():
                    file_stat = file_path.stat()
                    if file_stat.st_mtime < cutoff_time:
                        try:
                            file_path.unlink()
                            deleted_count += 1
                            logger.info(f"Deleted old file: {file_path}")
                        except Exception as e:
                            logger.error(f"Error deleting old file {file_path}: {e}")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
        
        logger.info(f"Cleanup completed. Deleted {deleted_count} old files.")
        return deleted_count


# Utility functions
def get_supported_file_types() -> Dict[str, str]:
    """Get dictionary of supported file extensions and their MIME types."""
    return FileHandler.SUPPORTED_EXTENSIONS.copy()


def is_text_file(filename: str) -> bool:
    """Check if file is a text-based file."""
    extension = Path(filename).suffix.lower()
    text_extensions = {'.txt', '.md'}
    return extension in text_extensions


def is_document_file(filename: str) -> bool:
    """Check if file is a document file."""
    extension = Path(filename).suffix.lower()
    document_extensions = {'.pdf', '.docx'}
    return extension in document_extensions
