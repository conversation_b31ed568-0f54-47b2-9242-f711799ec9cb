"""
Test script for RAG server functionality.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from core.config import settings
from core.database import SessionLocal, create_tables
from services.embedding_service import EmbeddingService
from services.vector_store_service import VectorStoreService
from services.document_service import DocumentService
from services.rag_service import RAGService


async def test_embedding_service():
    """Test embedding service functionality."""
    print("Testing Embedding Service...")
    
    embedding_service = EmbeddingService()
    
    # Test model info
    model_info = embedding_service.get_model_info()
    print(f"Model info: {model_info}")
    
    # Test health check
    health = await embedding_service.health_check()
    print(f"Health check: {health}")
    
    # Test embedding generation (if available)
    if health['model_loaded'] or health['api_available']:
        try:
            texts = ["This is a test sentence.", "Another test sentence."]
            embeddings, metadata = await embedding_service.generate_embeddings(texts)
            print(f"Generated {len(embeddings)} embeddings with dimension {len(embeddings[0]) if embeddings else 0}")
            print(f"Metadata: {metadata}")
        except Exception as e:
            print(f"Error generating embeddings: {e}")
    else:
        print("No embedding models available")
    
    print("Embedding Service test completed.\n")


async def test_vector_store_service():
    """Test vector store service functionality."""
    print("Testing Vector Store Service...")
    
    vector_store = VectorStoreService()
    
    # Test stats
    stats = vector_store.get_stats()
    print(f"Vector store stats: {stats}")
    
    # Test health check
    health = await vector_store.health_check()
    print(f"Health check: {health}")
    
    print("Vector Store Service test completed.\n")


async def test_document_service():
    """Test document service functionality."""
    print("Testing Document Service...")
    
    document_service = DocumentService()
    
    # Test file handler
    supported_types = document_service.file_handler.SUPPORTED_EXTENSIONS
    print(f"Supported file types: {list(supported_types.keys())}")
    
    # Test text processor
    text = "This is a test document. It has multiple sentences. We will test chunking."
    chunks = document_service.text_processor.chunk_text(text)
    print(f"Text chunked into {len(chunks)} chunks")
    
    for i, chunk in enumerate(chunks):
        print(f"Chunk {i}: {chunk.content[:50]}...")
    
    print("Document Service test completed.\n")


async def test_rag_service():
    """Test RAG service functionality."""
    print("Testing RAG Service...")
    
    rag_service = RAGService()
    
    # Test health check
    health = await rag_service.health_check()
    print(f"RAG Service health check: {health}")
    
    print("RAG Service test completed.\n")


async def test_database_setup():
    """Test database setup."""
    print("Testing Database Setup...")
    
    try:
        # Create tables
        await create_tables()
        print("Database tables created successfully")
        
        # Test database connection
        db = SessionLocal()
        try:
            # Simple query to test connection
            result = db.execute("SELECT 1").fetchone()
            print(f"Database connection test: {result}")
        finally:
            db.close()
        
    except Exception as e:
        print(f"Database setup error: {e}")
    
    print("Database setup test completed.\n")


async def main():
    """Run all tests."""
    print("=" * 50)
    print("RAG Server Test Suite")
    print("=" * 50)
    print()
    
    print(f"Configuration:")
    print(f"  App Name: {settings.app_name}")
    print(f"  Environment: {settings.app_environment}")
    print(f"  Database URL: {settings.database_url}")
    print(f"  Embedding Model: {settings.embedding_model}")
    print(f"  Vector Store Type: {settings.vector_store_type}")
    print(f"  Upload Directory: {settings.upload_dir}")
    print()
    
    # Run tests
    await test_database_setup()
    await test_embedding_service()
    await test_vector_store_service()
    await test_document_service()
    await test_rag_service()
    
    print("=" * 50)
    print("All tests completed!")
    print("=" * 50)


if __name__ == "__main__":
    asyncio.run(main())
