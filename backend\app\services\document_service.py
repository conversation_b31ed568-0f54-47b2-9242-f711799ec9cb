"""
Document service for processing and managing documents.
"""

import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from models.document import Document, DocumentChunk, ChunkEmbedding
from schemas.document import DocumentCreate, DocumentUpdate, DocumentFilters, PaginationParams
from utils.file_handlers import FileHandler
from utils.text_processing import TextProcessor
from services.embedding_service import EmbeddingService
from services.vector_store_service import VectorStoreService
from core.config import settings

logger = logging.getLogger(__name__)


class DocumentService:
    """Service for document processing and management."""
    
    def __init__(self):
        """Initialize document service."""
        self.file_handler = FileHandler(settings.upload_dir)
        self.text_processor = TextProcessor(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap
        )
        self.embedding_service = EmbeddingService()
        self.vector_store = VectorStoreService()
    
    async def create_document_from_file(
        self, 
        db: Session,
        file_content: bytes,
        filename: str,
        document_data: Optional[DocumentCreate] = None
    ) -> Document:
        """
        Create a new document from uploaded file.
        
        Args:
            db: Database session
            file_content: File content as bytes
            filename: Original filename
            document_data: Optional document metadata
            
        Returns:
            Created document instance
        """
        try:
            # Validate file
            if not self.file_handler.is_supported_file(filename):
                raise ValueError(f"Unsupported file type: {filename}")
            
            if not self.file_handler.validate_file_size(len(file_content), settings.max_file_size):
                raise ValueError(f"File too large: {len(file_content)} bytes")
            
            # Save file to disk
            file_path = await self.file_handler.save_uploaded_file(file_content, filename)
            file_info = self.file_handler.get_file_info(file_path)
            
            # Calculate content hash
            content_hash = self.file_handler.calculate_file_hash(file_path)
            
            # Check for duplicate
            existing_doc = db.query(Document).filter(Document.content_hash == content_hash).first()
            if existing_doc:
                # Delete the newly uploaded file since it's a duplicate
                await self.file_handler.delete_file(file_path)
                raise ValueError(f"Document already exists: {existing_doc.filename}")
            
            # Create document record
            document = Document(
                filename=file_path.name,
                original_filename=filename,
                file_path=str(file_path),
                file_size=file_info['file_size'],
                file_type=file_info['file_type'],
                mime_type=file_info['mime_type'],
                content_hash=content_hash,
                processing_status="pending"
            )
            
            # Add optional metadata
            if document_data:
                document.title = document_data.title
                document.description = document_data.description
                document.author = document_data.author
                document.language = document_data.language
                if document_data.tags:
                    document.tags = ",".join(document_data.tags)
            
            db.add(document)
            db.commit()
            db.refresh(document)
            
            logger.info(f"Created document: {document.id} - {filename}")
            
            # Start background processing
            asyncio.create_task(self._process_document_async(document.id))
            
            return document
            
        except Exception as e:
            logger.error(f"Error creating document from file: {e}")
            raise
    
    async def _process_document_async(self, document_id: int):
        """
        Process document asynchronously in the background.
        
        Args:
            document_id: Document ID to process
        """
        from core.database import SessionLocal
        
        db = SessionLocal()
        try:
            document = db.query(Document).filter(Document.id == document_id).first()
            if not document:
                logger.error(f"Document not found: {document_id}")
                return
            
            await self.process_document(db, document)
            
        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")
            # Update document status to failed
            document = db.query(Document).filter(Document.id == document_id).first()
            if document:
                document.processing_status = "failed"
                document.processing_error = str(e)
                db.commit()
        finally:
            db.close()
    
    async def process_document(self, db: Session, document: Document) -> bool:
        """
        Process a document: extract text, chunk, and generate embeddings.
        
        Args:
            db: Database session
            document: Document to process
            
        Returns:
            True if successful
        """
        try:
            # Update status
            document.processing_status = "processing"
            db.commit()
            
            # Extract text content
            file_path = Path(document.file_path)
            text_content = self.file_handler.extract_text_content(file_path)
            
            if not text_content.strip():
                raise ValueError("No text content extracted from document")
            
            # Clean and process text
            cleaned_text = self.text_processor.clean_text(text_content)
            document.content = cleaned_text
            
            # Extract metadata
            text_metadata = self.text_processor.extract_metadata(cleaned_text)
            document.word_count = text_metadata['word_count']
            document.character_count = text_metadata['character_count']
            
            # Chunk the text
            chunks = self.text_processor.chunk_text(cleaned_text)
            
            # Create chunk records
            chunk_records = []
            for chunk in chunks:
                chunk_record = DocumentChunk(
                    document_id=document.id,
                    content=chunk.content,
                    content_hash=self.text_processor.calculate_content_hash(chunk.content),
                    chunk_index=chunk.chunk_index,
                    start_char=chunk.start_char,
                    end_char=chunk.end_char,
                    word_count=chunk.word_count,
                    character_count=chunk.character_count
                )
                chunk_records.append(chunk_record)
            
            # Save chunks to database
            db.add_all(chunk_records)
            db.commit()
            
            # Refresh to get IDs
            for chunk_record in chunk_records:
                db.refresh(chunk_record)
            
            # Generate embeddings
            chunk_texts = [chunk.content for chunk in chunk_records]
            embeddings, embedding_metadata = await self.embedding_service.generate_embeddings(chunk_texts)
            
            # Save embeddings to database and vector store
            embedding_records = []
            chunk_ids = []
            
            for i, (chunk_record, embedding) in enumerate(zip(chunk_records, embeddings)):
                # Create embedding record
                embedding_record = ChunkEmbedding(
                    chunk_id=chunk_record.id,
                    model_name=embedding_metadata['model_used'],
                    dimension=len(embedding),
                    embedding_vector=self.embedding_service.serialize_embedding(embedding)
                )
                embedding_records.append(embedding_record)
                chunk_ids.append(chunk_record.id)
                
                # Update chunk record
                chunk_record.has_embedding = True
                chunk_record.embedding_model = embedding_metadata['model_used']
                chunk_record.embedding_dimension = len(embedding)
            
            # Save embedding records
            db.add_all(embedding_records)
            db.commit()
            
            # Add embeddings to vector store
            await self.vector_store.add_embeddings(embeddings, chunk_ids)
            
            # Update document status
            document.chunk_count = len(chunk_records)
            document.is_processed = True
            document.processing_status = "completed"
            document.processed_at = datetime.utcnow()
            document.processing_error = None
            
            db.commit()
            
            logger.info(f"Successfully processed document {document.id}: {len(chunk_records)} chunks, {len(embeddings)} embeddings")
            return True
            
        except Exception as e:
            logger.error(f"Error processing document {document.id}: {e}")
            document.processing_status = "failed"
            document.processing_error = str(e)
            db.commit()
            return False
    
    def get_documents(
        self, 
        db: Session,
        filters: Optional[DocumentFilters] = None,
        pagination: Optional[PaginationParams] = None
    ) -> Tuple[List[Document], int]:
        """
        Get documents with filtering and pagination.
        
        Args:
            db: Database session
            filters: Optional filters
            pagination: Optional pagination parameters
            
        Returns:
            Tuple of (documents list, total count)
        """
        query = db.query(Document)
        
        # Apply filters
        if filters:
            if filters.search:
                search_term = f"%{filters.search}%"
                query = query.filter(
                    or_(
                        Document.title.ilike(search_term),
                        Document.description.ilike(search_term),
                        Document.content.ilike(search_term),
                        Document.original_filename.ilike(search_term)
                    )
                )
            
            if filters.file_type:
                query = query.filter(Document.file_type == filters.file_type)
            
            if filters.author:
                query = query.filter(Document.author.ilike(f"%{filters.author}%"))
            
            if filters.language:
                query = query.filter(Document.language == filters.language)
            
            if filters.is_processed is not None:
                query = query.filter(Document.is_processed == filters.is_processed)
            
            if filters.date_from:
                query = query.filter(Document.created_at >= filters.date_from)
            
            if filters.date_to:
                query = query.filter(Document.created_at <= filters.date_to)
            
            if filters.tags:
                for tag in filters.tags:
                    query = query.filter(Document.tags.ilike(f"%{tag}%"))
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        if pagination:
            # Apply sorting
            if pagination.sort_by == "created_at":
                if pagination.sort_order == "desc":
                    query = query.order_by(Document.created_at.desc())
                else:
                    query = query.order_by(Document.created_at.asc())
            elif pagination.sort_by == "filename":
                if pagination.sort_order == "desc":
                    query = query.order_by(Document.filename.desc())
                else:
                    query = query.order_by(Document.filename.asc())
            elif pagination.sort_by == "file_size":
                if pagination.sort_order == "desc":
                    query = query.order_by(Document.file_size.desc())
                else:
                    query = query.order_by(Document.file_size.asc())
            
            # Apply pagination
            offset = (pagination.page - 1) * pagination.size
            query = query.offset(offset).limit(pagination.size)
        
        documents = query.all()
        return documents, total
    
    def get_document_by_id(self, db: Session, document_id: int) -> Optional[Document]:
        """Get document by ID."""
        return db.query(Document).filter(Document.id == document_id).first()
    
    def update_document(
        self, 
        db: Session, 
        document_id: int, 
        document_data: DocumentUpdate
    ) -> Optional[Document]:
        """Update document metadata."""
        document = self.get_document_by_id(db, document_id)
        if not document:
            return None
        
        # Update fields
        if document_data.title is not None:
            document.title = document_data.title
        if document_data.description is not None:
            document.description = document_data.description
        if document_data.author is not None:
            document.author = document_data.author
        if document_data.language is not None:
            document.language = document_data.language
        if document_data.tags is not None:
            document.tags = ",".join(document_data.tags)
        
        db.commit()
        db.refresh(document)
        return document
    
    async def delete_document(self, db: Session, document_id: int) -> bool:
        """Delete document and all associated data."""
        document = self.get_document_by_id(db, document_id)
        if not document:
            return False
        
        try:
            # Get chunk IDs for vector store removal
            chunk_ids = [chunk.id for chunk in document.chunks]
            
            # Remove from vector store
            if chunk_ids:
                await self.vector_store.remove_embeddings(chunk_ids)
            
            # Delete file from disk
            file_path = Path(document.file_path)
            await self.file_handler.delete_file(file_path)
            
            # Delete from database (cascades to chunks and embeddings)
            db.delete(document)
            db.commit()
            
            logger.info(f"Deleted document {document_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            db.rollback()
            return False
