"""
Pydantic schemas for RAG (Retrieval-Augmented Generation) API requests and responses.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from enum import Enum

from schemas.document import DocumentChunkWithSimilarity


class QueryType(str, Enum):
    """Enumeration of supported query types."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"


class ResponseFormat(str, Enum):
    """Enumeration of response formats."""
    TEXT = "text"
    MARKDOWN = "markdown"
    JSON = "json"


class RAGQueryBase(BaseModel):
    """Base schema for RAG queries."""
    query: str = Field(..., min_length=1, max_length=1000, description="User query")
    query_type: QueryType = Field(QueryType.SEMANTIC, description="Type of query processing")
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of results to retrieve")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity threshold")
    include_metadata: bool = Field(True, description="Include document metadata in response")


class RAGQuery(RAGQueryBase):
    """Schema for RAG query requests."""
    document_ids: Optional[List[int]] = Field(None, description="Limit search to specific documents")
    file_types: Optional[List[str]] = Field(None, description="Limit search to specific file types")
    authors: Optional[List[str]] = Field(None, description="Limit search to specific authors")
    tags: Optional[List[str]] = Field(None, description="Limit search to documents with specific tags")
    date_from: Optional[datetime] = Field(None, description="Limit search to documents created after this date")
    date_to: Optional[datetime] = Field(None, description="Limit search to documents created before this date")
    
    @validator('file_types')
    def validate_file_types(cls, v):
        if v is not None:
            allowed_types = ['pdf', 'txt', 'docx', 'md']
            for file_type in v:
                if file_type.lower() not in allowed_types:
                    raise ValueError(f'File type must be one of: {", ".join(allowed_types)}')
            return [ft.lower() for ft in v]
        return v


class RAGGenerationQuery(RAGQueryBase):
    """Schema for RAG query with text generation."""
    generate_response: bool = Field(True, description="Generate AI response using retrieved context")
    response_format: ResponseFormat = Field(ResponseFormat.TEXT, description="Format of generated response")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="LLM temperature for generation")
    max_tokens: int = Field(1000, ge=50, le=4000, description="Maximum tokens in generated response")
    system_prompt: Optional[str] = Field(None, max_length=500, description="Custom system prompt")
    include_sources: bool = Field(True, description="Include source references in response")
    
    # Inherit all filters from RAGQuery
    document_ids: Optional[List[int]] = Field(None, description="Limit search to specific documents")
    file_types: Optional[List[str]] = Field(None, description="Limit search to specific file types")
    authors: Optional[List[str]] = Field(None, description="Limit search to specific authors")
    tags: Optional[List[str]] = Field(None, description="Limit search to documents with specific tags")
    date_from: Optional[datetime] = Field(None, description="Limit search to documents created after this date")
    date_to: Optional[datetime] = Field(None, description="Limit search to documents created before this date")


class RetrievalResult(BaseModel):
    """Schema for individual retrieval result."""
    chunk: DocumentChunkWithSimilarity
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="Relevance score")
    rank: int = Field(..., ge=1, description="Result rank")


class RAGRetrievalResponse(BaseModel):
    """Schema for RAG retrieval response."""
    query: str
    query_type: QueryType
    results: List[RetrievalResult]
    total_results: int
    max_results: int
    similarity_threshold: float
    processing_time_ms: float
    timestamp: datetime


class GeneratedResponse(BaseModel):
    """Schema for AI-generated response."""
    content: str = Field(..., description="Generated response content")
    format: ResponseFormat = Field(..., description="Response format")
    model_used: str = Field(..., description="LLM model used for generation")
    temperature: float = Field(..., description="Temperature used for generation")
    tokens_used: int = Field(..., description="Number of tokens used")
    sources_count: int = Field(..., description="Number of source documents used")


class RAGGenerationResponse(BaseModel):
    """Schema for RAG generation response."""
    query: str
    retrieval: RAGRetrievalResponse
    generated_response: GeneratedResponse
    total_processing_time_ms: float
    timestamp: datetime


class RAGChatMessage(BaseModel):
    """Schema for chat message in conversation."""
    role: str = Field(..., regex="^(user|assistant|system)$", description="Message role")
    content: str = Field(..., min_length=1, description="Message content")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional message metadata")


class RAGChatQuery(BaseModel):
    """Schema for conversational RAG query."""
    message: str = Field(..., min_length=1, max_length=1000, description="User message")
    conversation_history: Optional[List[RAGChatMessage]] = Field(None, description="Previous conversation messages")
    context_window: int = Field(5, ge=1, le=20, description="Number of previous messages to consider")
    
    # RAG parameters
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of results to retrieve")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="Minimum similarity threshold")
    temperature: float = Field(0.7, ge=0.0, le=2.0, description="LLM temperature for generation")
    max_tokens: int = Field(1000, ge=50, le=4000, description="Maximum tokens in generated response")
    
    # Filters
    document_ids: Optional[List[int]] = Field(None, description="Limit search to specific documents")
    file_types: Optional[List[str]] = Field(None, description="Limit search to specific file types")


class RAGChatResponse(BaseModel):
    """Schema for conversational RAG response."""
    message: RAGChatMessage
    retrieval_results: List[RetrievalResult]
    processing_time_ms: float
    timestamp: datetime


class EmbeddingRequest(BaseModel):
    """Schema for embedding generation request."""
    texts: List[str] = Field(..., min_items=1, max_items=100, description="Texts to embed")
    model: Optional[str] = Field(None, description="Embedding model to use")
    normalize: bool = Field(True, description="Normalize embeddings")


class EmbeddingResponse(BaseModel):
    """Schema for embedding generation response."""
    embeddings: List[List[float]] = Field(..., description="Generated embeddings")
    model_used: str = Field(..., description="Model used for embedding")
    dimension: int = Field(..., description="Embedding dimension")
    processing_time_ms: float = Field(..., description="Processing time in milliseconds")


class SimilaritySearchRequest(BaseModel):
    """Schema for similarity search request."""
    query_embedding: List[float] = Field(..., description="Query embedding vector")
    max_results: int = Field(10, ge=1, le=50, description="Maximum number of results")
    similarity_threshold: float = Field(0.0, ge=0.0, le=1.0, description="Minimum similarity threshold")
    document_ids: Optional[List[int]] = Field(None, description="Limit search to specific documents")


class RAGSystemStatus(BaseModel):
    """Schema for RAG system status."""
    status: str = Field(..., description="System status")
    total_documents: int = Field(..., description="Total number of documents")
    total_chunks: int = Field(..., description="Total number of chunks")
    total_embeddings: int = Field(..., description="Total number of embeddings")
    embedding_model: str = Field(..., description="Current embedding model")
    vector_store_type: str = Field(..., description="Vector store type")
    last_updated: datetime = Field(..., description="Last system update")
    health_checks: Dict[str, bool] = Field(..., description="Component health status")
