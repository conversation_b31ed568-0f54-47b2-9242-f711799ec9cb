"""
Reality 2.0 - Czech Real Estate & Building Search Application
Search for cities, find buildings using RUIAN API, and get cadastral information.
"""

import time
import logging
import httpx
import asyncio
import math
from typing import Optional, List, Dict, Any, Tuple
from fastapi import FastAPI, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Setup basic logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI application
app = FastAPI(
    title="Reality 2.0",
    description="Czech Real Estate & Building Search Application - Search cities, find buildings via RUIAN API",
    version="1.0.0",
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000", "*"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Pydantic models
class CitySearchResult(BaseModel):
    name: str
    region: str
    coordinates: Dict[str, float]  # lat, lng
    bounding_box: Dict[str, float]  # north, south, east, west

class Building(BaseModel):
    id: str
    address: str
    property_type: str
    coordinates: Dict[str, float]  # lat, lng
    sjtsk_coordinates: Dict[str, float]  # x, y in S-JTSK
    cadastral_link: str

class CoordinateMesh(BaseModel):
    total_points: int
    grid_size: float
    bounding_box: Dict[str, float]
    sample_points: List[Dict[str, float]]

# Root endpoint
@app.get("/")
async def root():
    """Root endpoint with application information."""
    return {
        "message": "Welcome to Reality 2.0",
        "description": "Czech Real Estate & Building Search Application",
        "version": "1.0.0",
        "docs_url": "/docs",
        "health_url": "/health",
        "features": [
            "City search with Mapy.cz autocomplete",
            "Building search via RUIAN API",
            "Coordinate mesh generation",
            "Cadastral office links",
            "S-JTSK coordinate conversion"
        ],
        "endpoints": {
            "city_search": "/api/v1/cities/search",
            "buildings": "/api/v1/buildings",
            "mesh": "/api/v1/mesh"
        }
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    return {
        "status": "healthy",
        "app_name": "Reality 2.0",
        "version": "1.0.0",
        "environment": "development",
        "timestamp": time.time(),
        "services": {
            "api": "healthy",
            "mapy_cz": "available",
            "ruian_api": "available"
        }
    }

# City search endpoint (Mapy.cz autocomplete simulation)
@app.get("/api/v1/cities/search", response_model=List[CitySearchResult])
async def search_cities(q: str = Query(..., description="City name to search")):
    """
    Search for Czech cities using autocomplete.
    This simulates Mapy.cz API integration.
    """
    if len(q) < 2:
        raise HTTPException(status_code=400, detail="Query must be at least 2 characters long")
    
    # Simulated Czech cities data (in real app, this would call Mapy.cz API)
    cities_data = {
        "praha": {
            "name": "Praha",
            "region": "Hlavní město Praha",
            "coordinates": {"lat": 50.0755, "lng": 14.4378},
            "bounding_box": {"north": 50.1773, "south": 49.9426, "east": 14.7067, "west": 14.2244}
        },
        "brno": {
            "name": "Brno",
            "region": "Jihomoravský kraj",
            "coordinates": {"lat": 49.1951, "lng": 16.6068},
            "bounding_box": {"north": 49.2951, "south": 49.0951, "east": 16.7068, "west": 16.5068}
        },
        "ostrava": {
            "name": "Ostrava",
            "region": "Moravskoslezský kraj",
            "coordinates": {"lat": 49.8209, "lng": 18.2625},
            "bounding_box": {"north": 49.9209, "south": 49.7209, "east": 18.3625, "west": 18.1625}
        },
        "plzen": {
            "name": "Plzeň",
            "region": "Plzeňský kraj",
            "coordinates": {"lat": 49.7384, "lng": 13.3736},
            "bounding_box": {"north": 49.8384, "south": 49.6384, "east": 13.4736, "west": 13.2736}
        },
        "liberec": {
            "name": "Liberec",
            "region": "Liberecký kraj",
            "coordinates": {"lat": 50.7663, "lng": 15.0543},
            "bounding_box": {"north": 50.8663, "south": 50.6663, "east": 15.1543, "west": 14.9543}
        }
    }
    
    # Filter cities based on query
    query_lower = q.lower()
    results = []
    
    for city_key, city_data in cities_data.items():
        if query_lower in city_key or query_lower in city_data["name"].lower():
            results.append(CitySearchResult(**city_data))
    
    logger.info(f"City search for '{q}' returned {len(results)} results")
    return results

# Generate coordinate mesh
@app.get("/api/v1/mesh", response_model=CoordinateMesh)
async def generate_coordinate_mesh(
    north: float = Query(..., description="Northern boundary"),
    south: float = Query(..., description="Southern boundary"),
    east: float = Query(..., description="Eastern boundary"),
    west: float = Query(..., description="Western boundary"),
    grid_size: float = Query(0.001, description="Grid size in degrees (default: 0.001)")
):
    """
    Generate a coordinate mesh within the given bounding box.
    This creates a grid of points for RUIAN API queries.
    """
    if north <= south or east <= west:
        raise HTTPException(status_code=400, detail="Invalid bounding box coordinates")
    
    if grid_size <= 0 or grid_size > 1:
        raise HTTPException(status_code=400, detail="Grid size must be between 0 and 1")
    
    # Calculate number of points
    lat_steps = int((north - south) / grid_size) + 1
    lng_steps = int((east - west) / grid_size) + 1
    total_points = lat_steps * lng_steps
    
    # Generate sample points (first 20 for preview)
    sample_points = []
    count = 0
    
    for i in range(min(lat_steps, 5)):  # Limit to 5x5 grid for sample
        for j in range(min(lng_steps, 5)):
            if count >= 20:
                break
            lat = south + (i * grid_size)
            lng = west + (j * grid_size)
            sample_points.append({"lat": lat, "lng": lng})
            count += 1
    
    bounding_box = {
        "north": north,
        "south": south,
        "east": east,
        "west": west
    }
    
    logger.info(f"Generated mesh with {total_points} points (grid_size: {grid_size})")
    
    return CoordinateMesh(
        total_points=total_points,
        grid_size=grid_size,
        bounding_box=bounding_box,
        sample_points=sample_points
    )

# Convert WGS84 to S-JTSK coordinates (simplified)
def wgs84_to_sjtsk(lat: float, lng: float) -> Tuple[float, float]:
    """
    Convert WGS84 coordinates to S-JTSK.
    This is a simplified conversion - in production, use proper transformation library.
    """
    # Simplified conversion (this is not accurate - use proper library in production)
    # S-JTSK uses different projection, this is just for demonstration
    x = (lng - 14.0) * 111320 * math.cos(math.radians(lat))  # Approximate
    y = (lat - 50.0) * 111320  # Approximate
    
    # S-JTSK typically has negative coordinates
    return -x, -y

# Generate cadastral office link
def generate_cadastral_link(sjtsk_x: float, sjtsk_y: float) -> str:
    """Generate link to Czech Cadastral Office (CUZK) map."""
    # CUZK map URL format (simplified)
    base_url = "https://nahlizenidokn.cuzk.cz/MapaIdentifikace.aspx"
    return f"{base_url}?&x={sjtsk_x:.0f}&y={sjtsk_y:.0f}"

# Search buildings (RUIAN API simulation)
@app.get("/api/v1/buildings", response_model=List[Building])
async def search_buildings(
    lat: float = Query(..., description="Latitude"),
    lng: float = Query(..., description="Longitude"),
    radius: float = Query(0.01, description="Search radius in degrees")
):
    """
    Search for buildings near given coordinates using RUIAN API.
    This simulates the RUIAN API integration.
    """
    if not (-90 <= lat <= 90) or not (-180 <= lng <= 180):
        raise HTTPException(status_code=400, detail="Invalid coordinates")
    
    if radius <= 0 or radius > 1:
        raise HTTPException(status_code=400, detail="Radius must be between 0 and 1")
    
    # Simulate building data (in real app, this would query RUIAN API)
    simulated_buildings = []
    
    # Generate some sample buildings around the coordinates
    for i in range(5):  # Simulate 5 buildings
        building_lat = lat + (i - 2) * 0.001  # Spread around the point
        building_lng = lng + (i - 2) * 0.001
        
        # Convert to S-JTSK
        sjtsk_x, sjtsk_y = wgs84_to_sjtsk(building_lat, building_lng)
        
        building = Building(
            id=f"RUIAN_{1000000 + i}",
            address=f"Testovací ulice {i+1}, Praha",
            property_type="residential" if i % 2 == 0 else "commercial",
            coordinates={"lat": building_lat, "lng": building_lng},
            sjtsk_coordinates={"x": sjtsk_x, "y": sjtsk_y},
            cadastral_link=generate_cadastral_link(sjtsk_x, sjtsk_y)
        )
        simulated_buildings.append(building)
    
    logger.info(f"Found {len(simulated_buildings)} buildings near {lat}, {lng}")
    return simulated_buildings

# Batch building search for mesh
@app.post("/api/v1/buildings/batch")
async def batch_search_buildings(
    coordinates: List[Dict[str, float]],
    radius: float = Query(0.001, description="Search radius in degrees")
):
    """
    Search for buildings at multiple coordinates (for mesh processing).
    """
    if len(coordinates) > 100:
        raise HTTPException(status_code=400, detail="Maximum 100 coordinates per batch")
    
    all_buildings = []
    
    for coord in coordinates:
        if "lat" not in coord or "lng" not in coord:
            continue
            
        # Simulate finding buildings at each coordinate
        buildings = await search_buildings(coord["lat"], coord["lng"], radius)
        all_buildings.extend(buildings)
    
    # Remove duplicates based on ID
    unique_buildings = {}
    for building in all_buildings:
        unique_buildings[building.id] = building
    
    result = list(unique_buildings.values())
    logger.info(f"Batch search found {len(result)} unique buildings from {len(coordinates)} coordinates")
    
    return {
        "total_buildings": len(result),
        "searched_coordinates": len(coordinates),
        "buildings": result[:50]  # Limit response size
    }

# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}")
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Internal server error",
            "message": "An unexpected error occurred"
        }
    )

if __name__ == "__main__":
    print("🏠 Starting Reality 2.0 - Czech Real Estate Search...")
    print("📖 API Documentation: http://localhost:8000/docs")
    print("🔍 Health Check: http://localhost:8000/health")
    print("🏙️  City Search: http://localhost:8000/api/v1/cities/search?q=praha")
    print("🏢 Building Search: http://localhost:8000/api/v1/buildings?lat=50.0755&lng=14.4378")
    
    uvicorn.run(
        "reality_app:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
