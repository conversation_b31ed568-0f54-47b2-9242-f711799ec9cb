"""
CRUD operations for documents.
"""

from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc

from models.document import Document, DocumentChunk, ChunkEmbedding
from schemas.document import DocumentCreate, DocumentUpdate, DocumentFilters, PaginationParams


def create_document(db: Session, document: DocumentCreate, **kwargs) -> Document:
    """
    Create a new document.
    
    Args:
        db: Database session
        document: Document creation data
        **kwargs: Additional fields for the document
        
    Returns:
        Created document
    """
    db_document = Document(
        title=document.title,
        description=document.description,
        author=document.author,
        language=document.language,
        tags=",".join(document.tags) if document.tags else None,
        **kwargs
    )
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    return db_document


def get_document(db: Session, document_id: int) -> Optional[Document]:
    """
    Get document by ID.
    
    Args:
        db: Database session
        document_id: Document ID
        
    Returns:
        Document or None if not found
    """
    return db.query(Document).filter(Document.id == document_id).first()


def get_document_by_hash(db: Session, content_hash: str) -> Optional[Document]:
    """
    Get document by content hash.
    
    Args:
        db: Database session
        content_hash: Content hash
        
    Returns:
        Document or None if not found
    """
    return db.query(Document).filter(Document.content_hash == content_hash).first()


def get_documents(
    db: Session,
    filters: Optional[DocumentFilters] = None,
    pagination: Optional[PaginationParams] = None
) -> Tuple[List[Document], int]:
    """
    Get documents with filtering and pagination.
    
    Args:
        db: Database session
        filters: Optional filters
        pagination: Optional pagination parameters
        
    Returns:
        Tuple of (documents list, total count)
    """
    query = db.query(Document)
    
    # Apply filters
    if filters:
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    Document.title.ilike(search_term),
                    Document.description.ilike(search_term),
                    Document.content.ilike(search_term),
                    Document.original_filename.ilike(search_term),
                    Document.author.ilike(search_term)
                )
            )
        
        if filters.file_type:
            query = query.filter(Document.file_type == filters.file_type)
        
        if filters.author:
            query = query.filter(Document.author.ilike(f"%{filters.author}%"))
        
        if filters.language:
            query = query.filter(Document.language == filters.language)
        
        if filters.is_processed is not None:
            query = query.filter(Document.is_processed == filters.is_processed)
        
        if filters.date_from:
            query = query.filter(Document.created_at >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(Document.created_at <= filters.date_to)
        
        if filters.tags:
            for tag in filters.tags:
                query = query.filter(Document.tags.ilike(f"%{tag}%"))
    
    # Get total count before pagination
    total = query.count()
    
    # Apply pagination and sorting
    if pagination:
        # Apply sorting
        sort_column = getattr(Document, pagination.sort_by, Document.created_at)
        if pagination.sort_order == "desc":
            query = query.order_by(desc(sort_column))
        else:
            query = query.order_by(asc(sort_column))
        
        # Apply pagination
        offset = (pagination.page - 1) * pagination.size
        query = query.offset(offset).limit(pagination.size)
    else:
        # Default sorting
        query = query.order_by(desc(Document.created_at))
    
    documents = query.all()
    return documents, total


def update_document(
    db: Session, 
    document_id: int, 
    document_update: DocumentUpdate
) -> Optional[Document]:
    """
    Update document.
    
    Args:
        db: Database session
        document_id: Document ID
        document_update: Update data
        
    Returns:
        Updated document or None if not found
    """
    document = get_document(db, document_id)
    if not document:
        return None
    
    update_data = document_update.dict(exclude_unset=True)
    
    # Handle tags specially
    if 'tags' in update_data and update_data['tags'] is not None:
        update_data['tags'] = ",".join(update_data['tags'])
    
    for field, value in update_data.items():
        setattr(document, field, value)
    
    db.commit()
    db.refresh(document)
    return document


def delete_document(db: Session, document_id: int) -> bool:
    """
    Delete document.
    
    Args:
        db: Database session
        document_id: Document ID
        
    Returns:
        True if deleted, False if not found
    """
    document = get_document(db, document_id)
    if not document:
        return False
    
    db.delete(document)
    db.commit()
    return True


def get_document_chunks(
    db: Session, 
    document_id: int,
    limit: Optional[int] = None,
    offset: Optional[int] = None
) -> List[DocumentChunk]:
    """
    Get chunks for a document.
    
    Args:
        db: Database session
        document_id: Document ID
        limit: Optional limit
        offset: Optional offset
        
    Returns:
        List of document chunks
    """
    query = db.query(DocumentChunk).filter(DocumentChunk.document_id == document_id)
    query = query.order_by(DocumentChunk.chunk_index)
    
    if offset:
        query = query.offset(offset)
    if limit:
        query = query.limit(limit)
    
    return query.all()


def get_chunk_by_id(db: Session, chunk_id: int) -> Optional[DocumentChunk]:
    """
    Get chunk by ID.
    
    Args:
        db: Database session
        chunk_id: Chunk ID
        
    Returns:
        Document chunk or None if not found
    """
    return db.query(DocumentChunk).filter(DocumentChunk.id == chunk_id).first()


def get_chunks_by_ids(db: Session, chunk_ids: List[int]) -> List[DocumentChunk]:
    """
    Get chunks by IDs.
    
    Args:
        db: Database session
        chunk_ids: List of chunk IDs
        
    Returns:
        List of document chunks
    """
    return db.query(DocumentChunk).filter(DocumentChunk.id.in_(chunk_ids)).all()


def get_document_statistics(db: Session) -> Dict[str, Any]:
    """
    Get document statistics.
    
    Args:
        db: Database session
        
    Returns:
        Dictionary with statistics
    """
    total_documents = db.query(func.count(Document.id)).scalar()
    processed_documents = db.query(func.count(Document.id)).filter(
        Document.is_processed == True
    ).scalar()
    pending_documents = db.query(func.count(Document.id)).filter(
        Document.processing_status == "pending"
    ).scalar()
    failed_documents = db.query(func.count(Document.id)).filter(
        Document.processing_status == "failed"
    ).scalar()
    
    total_chunks = db.query(func.count(DocumentChunk.id)).scalar()
    total_embeddings = db.query(func.count(ChunkEmbedding.id)).scalar()
    
    total_file_size = db.query(func.sum(Document.file_size)).scalar() or 0
    
    # Average chunk size
    avg_chunk_size = db.query(func.avg(DocumentChunk.character_count)).scalar() or 0
    
    # File type distribution
    file_type_stats = db.query(
        Document.file_type,
        func.count(Document.id).label('count')
    ).group_by(Document.file_type).all()
    
    # Processing status distribution
    status_stats = db.query(
        Document.processing_status,
        func.count(Document.id).label('count')
    ).group_by(Document.processing_status).all()
    
    return {
        'total_documents': total_documents,
        'processed_documents': processed_documents,
        'pending_documents': pending_documents,
        'failed_documents': failed_documents,
        'total_chunks': total_chunks,
        'total_embeddings': total_embeddings,
        'total_file_size': total_file_size,
        'average_chunk_size': float(avg_chunk_size),
        'file_type_distribution': {ft: count for ft, count in file_type_stats},
        'status_distribution': {status: count for status, count in status_stats}
    }


def search_documents_by_content(
    db: Session,
    search_term: str,
    limit: int = 10
) -> List[Document]:
    """
    Search documents by content.
    
    Args:
        db: Database session
        search_term: Search term
        limit: Maximum number of results
        
    Returns:
        List of matching documents
    """
    search_pattern = f"%{search_term}%"
    
    return db.query(Document).filter(
        or_(
            Document.title.ilike(search_pattern),
            Document.description.ilike(search_pattern),
            Document.content.ilike(search_pattern),
            Document.author.ilike(search_pattern)
        )
    ).filter(
        Document.is_processed == True
    ).order_by(
        desc(Document.created_at)
    ).limit(limit).all()


def get_documents_by_status(
    db: Session,
    status: str,
    limit: Optional[int] = None
) -> List[Document]:
    """
    Get documents by processing status.
    
    Args:
        db: Database session
        status: Processing status
        limit: Optional limit
        
    Returns:
        List of documents
    """
    query = db.query(Document).filter(Document.processing_status == status)
    query = query.order_by(desc(Document.created_at))
    
    if limit:
        query = query.limit(limit)
    
    return query.all()


def get_recent_documents(
    db: Session,
    limit: int = 10
) -> List[Document]:
    """
    Get recently created documents.
    
    Args:
        db: Database session
        limit: Maximum number of results
        
    Returns:
        List of recent documents
    """
    return db.query(Document).order_by(
        desc(Document.created_at)
    ).limit(limit).all()


def get_documents_by_author(
    db: Session,
    author: str,
    limit: Optional[int] = None
) -> List[Document]:
    """
    Get documents by author.
    
    Args:
        db: Database session
        author: Author name
        limit: Optional limit
        
    Returns:
        List of documents
    """
    query = db.query(Document).filter(
        Document.author.ilike(f"%{author}%")
    ).order_by(desc(Document.created_at))
    
    if limit:
        query = query.limit(limit)
    
    return query.all()


def get_documents_by_tags(
    db: Session,
    tags: List[str],
    match_all: bool = False,
    limit: Optional[int] = None
) -> List[Document]:
    """
    Get documents by tags.
    
    Args:
        db: Database session
        tags: List of tags
        match_all: If True, document must have all tags; if False, any tag
        limit: Optional limit
        
    Returns:
        List of documents
    """
    query = db.query(Document)
    
    if match_all:
        # Document must have all tags
        for tag in tags:
            query = query.filter(Document.tags.ilike(f"%{tag}%"))
    else:
        # Document must have at least one tag
        tag_filters = [Document.tags.ilike(f"%{tag}%") for tag in tags]
        query = query.filter(or_(*tag_filters))
    
    query = query.order_by(desc(Document.created_at))
    
    if limit:
        query = query.limit(limit)
    
    return query.all()
