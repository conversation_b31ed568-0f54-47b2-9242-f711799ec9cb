"""
Vector store service for storing and retrieving embeddings.
"""

import os
import pickle
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging
import numpy as np

# Import vector store libraries
try:
    import faiss
    FAISS_AVAILABLE = True
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("faiss-cpu not available. FAISS vector store will be disabled.")

from core.config import settings

logger = logging.getLogger(__name__)


class VectorStoreService:
    """Service for managing vector storage and similarity search."""
    
    def __init__(self):
        """Initialize vector store service."""
        self.store_type = settings.vector_store_type
        self.store_path = Path(settings.vector_store_path)
        self.dimension = settings.embedding_dimension
        
        # Create store directory
        self.store_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize vector store
        self._index = None
        self._metadata = {}
        self._id_mapping = {}  # Maps internal IDs to chunk IDs
        self._next_id = 0
        
        self._initialize_store()
    
    def _initialize_store(self):
        """Initialize the vector store based on configuration."""
        if self.store_type == 'faiss':
            self._initialize_faiss()
        else:
            logger.warning(f"Unsupported vector store type: {self.store_type}")
    
    def _initialize_faiss(self):
        """Initialize FAISS vector store."""
        if not FAISS_AVAILABLE:
            raise RuntimeError("FAISS not available. Install faiss-cpu.")
        
        index_path = self.store_path / "faiss_index.bin"
        metadata_path = self.store_path / "metadata.json"
        mapping_path = self.store_path / "id_mapping.json"
        
        try:
            if index_path.exists():
                # Load existing index
                self._index = faiss.read_index(str(index_path))
                logger.info(f"Loaded existing FAISS index with {self._index.ntotal} vectors")
                
                # Load metadata
                if metadata_path.exists():
                    with open(metadata_path, 'r') as f:
                        self._metadata = json.load(f)
                
                # Load ID mapping
                if mapping_path.exists():
                    with open(mapping_path, 'r') as f:
                        mapping_data = json.load(f)
                        self._id_mapping = {int(k): v for k, v in mapping_data.items()}
                        self._next_id = max(self._id_mapping.keys()) + 1 if self._id_mapping else 0
            else:
                # Create new index
                self._index = faiss.IndexFlatIP(self.dimension)  # Inner product (cosine similarity for normalized vectors)
                logger.info(f"Created new FAISS index with dimension {self.dimension}")
                
        except Exception as e:
            logger.error(f"Error initializing FAISS: {e}")
            # Fallback to new index
            self._index = faiss.IndexFlatIP(self.dimension)
    
    async def add_embeddings(
        self, 
        embeddings: List[List[float]], 
        chunk_ids: List[int],
        metadata: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """
        Add embeddings to the vector store.
        
        Args:
            embeddings: List of embedding vectors
            chunk_ids: List of corresponding chunk IDs
            metadata: Optional metadata for each embedding
            
        Returns:
            True if successful
        """
        if not embeddings or len(embeddings) != len(chunk_ids):
            raise ValueError("Embeddings and chunk_ids must have the same length")
        
        try:
            # Convert to numpy array
            embedding_array = np.array(embeddings, dtype=np.float32)
            
            # Normalize embeddings for cosine similarity
            faiss.normalize_L2(embedding_array)
            
            # Add to index
            if self.store_type == 'faiss':
                self._index.add(embedding_array)
            
            # Update ID mapping and metadata
            for i, chunk_id in enumerate(chunk_ids):
                internal_id = self._next_id + i
                self._id_mapping[internal_id] = chunk_id
                
                if metadata and i < len(metadata):
                    self._metadata[str(chunk_id)] = metadata[i]
            
            self._next_id += len(chunk_ids)
            
            # Save to disk
            await self._save_index()
            
            logger.info(f"Added {len(embeddings)} embeddings to vector store")
            return True
            
        except Exception as e:
            logger.error(f"Error adding embeddings to vector store: {e}")
            return False
    
    async def search_similar(
        self, 
        query_embedding: List[float], 
        top_k: int = 10,
        threshold: float = 0.0
    ) -> List[Tuple[int, float]]:
        """
        Search for similar embeddings.
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of top results to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of (chunk_id, similarity_score) tuples
        """
        if not self._index or self._index.ntotal == 0:
            return []
        
        try:
            # Convert to numpy array and normalize
            query_array = np.array([query_embedding], dtype=np.float32)
            faiss.normalize_L2(query_array)
            
            # Search
            if self.store_type == 'faiss':
                scores, indices = self._index.search(query_array, min(top_k, self._index.ntotal))
            else:
                return []
            
            # Convert results
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1:  # FAISS returns -1 for invalid indices
                    continue
                
                # Convert internal ID to chunk ID
                chunk_id = self._id_mapping.get(int(idx))
                if chunk_id is not None and score >= threshold:
                    results.append((chunk_id, float(score)))
            
            logger.debug(f"Found {len(results)} similar embeddings")
            return results
            
        except Exception as e:
            logger.error(f"Error searching vector store: {e}")
            return []
    
    async def remove_embeddings(self, chunk_ids: List[int]) -> bool:
        """
        Remove embeddings from the vector store.
        
        Args:
            chunk_ids: List of chunk IDs to remove
            
        Returns:
            True if successful
        """
        try:
            # For FAISS, we need to rebuild the index without the removed embeddings
            # This is because FAISS doesn't support efficient removal
            
            if self.store_type == 'faiss' and chunk_ids:
                # Get all current embeddings except the ones to remove
                remaining_chunk_ids = []
                remaining_embeddings = []
                
                # This is a simplified approach - in production, you might want to
                # maintain a separate mapping of embeddings for efficient removal
                for internal_id, chunk_id in self._id_mapping.items():
                    if chunk_id not in chunk_ids:
                        remaining_chunk_ids.append(chunk_id)
                        # Note: We would need to store embeddings separately to reconstruct
                
                # Remove from metadata
                for chunk_id in chunk_ids:
                    self._metadata.pop(str(chunk_id), None)
                
                # Update ID mapping
                new_mapping = {}
                new_id = 0
                for internal_id, chunk_id in self._id_mapping.items():
                    if chunk_id not in chunk_ids:
                        new_mapping[new_id] = chunk_id
                        new_id += 1
                
                self._id_mapping = new_mapping
                self._next_id = new_id
                
                # Save changes
                await self._save_index()
                
                logger.info(f"Removed {len(chunk_ids)} embeddings from vector store")
            
            return True
            
        except Exception as e:
            logger.error(f"Error removing embeddings from vector store: {e}")
            return False
    
    async def _save_index(self):
        """Save the vector index and metadata to disk."""
        try:
            if self.store_type == 'faiss' and self._index:
                index_path = self.store_path / "faiss_index.bin"
                metadata_path = self.store_path / "metadata.json"
                mapping_path = self.store_path / "id_mapping.json"
                
                # Save index
                faiss.write_index(self._index, str(index_path))
                
                # Save metadata
                with open(metadata_path, 'w') as f:
                    json.dump(self._metadata, f, indent=2)
                
                # Save ID mapping
                with open(mapping_path, 'w') as f:
                    json.dump(self._id_mapping, f, indent=2)
                
                logger.debug("Saved vector store to disk")
                
        except Exception as e:
            logger.error(f"Error saving vector store: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Get vector store statistics.
        
        Returns:
            Dictionary with store statistics
        """
        stats = {
            'store_type': self.store_type,
            'dimension': self.dimension,
            'total_vectors': 0,
            'total_chunks': len(self._id_mapping),
            'store_path': str(self.store_path),
            'index_loaded': self._index is not None
        }
        
        if self._index:
            if self.store_type == 'faiss':
                stats['total_vectors'] = self._index.ntotal
                stats['index_type'] = type(self._index).__name__
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on vector store.
        
        Returns:
            Health check results
        """
        health = {
            'status': 'healthy',
            'store_initialized': False,
            'store_writable': False,
            'test_search_success': False
        }
        
        try:
            # Check if store is initialized
            if self._index is not None:
                health['store_initialized'] = True
            
            # Check if store directory is writable
            test_file = self.store_path / "test_write.tmp"
            try:
                test_file.write_text("test")
                test_file.unlink()
                health['store_writable'] = True
            except Exception:
                health['store_writable'] = False
            
            # Test search functionality
            if self._index and self._index.ntotal > 0:
                test_embedding = [0.1] * self.dimension
                results = await self.search_similar(test_embedding, top_k=1)
                health['test_search_success'] = True
            
        except Exception as e:
            health['status'] = 'unhealthy'
            health['error'] = str(e)
            logger.error(f"Vector store health check failed: {e}")
        
        return health
    
    async def rebuild_index(self) -> bool:
        """
        Rebuild the vector index from scratch.
        
        Returns:
            True if successful
        """
        try:
            # Create new index
            if self.store_type == 'faiss':
                self._index = faiss.IndexFlatIP(self.dimension)
            
            # Reset mappings
            self._id_mapping = {}
            self._next_id = 0
            
            # Save empty index
            await self._save_index()
            
            logger.info("Rebuilt vector index")
            return True
            
        except Exception as e:
            logger.error(f"Error rebuilding vector index: {e}")
            return False
    
    def get_chunk_metadata(self, chunk_id: int) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a specific chunk.
        
        Args:
            chunk_id: Chunk ID
            
        Returns:
            Metadata dictionary or None if not found
        """
        return self._metadata.get(str(chunk_id))
