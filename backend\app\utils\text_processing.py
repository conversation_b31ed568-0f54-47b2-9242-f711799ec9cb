"""
Text processing utilities for document chunking and preprocessing.
"""

import re
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class TextChunk:
    """Data class for text chunks."""
    content: str
    start_char: int
    end_char: int
    chunk_index: int
    word_count: int
    character_count: int
    metadata: Optional[Dict[str, Any]] = None


class TextProcessor:
    """Text processing utility class for document chunking and preprocessing."""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        """
        Initialize text processor.
        
        Args:
            chunk_size: Maximum size of each chunk in characters
            chunk_overlap: Number of overlapping characters between chunks
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize text content.
        
        Args:
            text: Raw text content
            
        Returns:
            Cleaned text content
        """
        if not text:
            return ""
            
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove control characters except newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        # Normalize quotes
        text = re.sub(r'["""]', '"', text)
        text = re.sub(r'[''']', "'", text)
        
        # Remove excessive punctuation
        text = re.sub(r'[.]{3,}', '...', text)
        text = re.sub(r'[!]{2,}', '!', text)
        text = re.sub(r'[?]{2,}', '?', text)
        
        return text.strip()
    
    def extract_metadata(self, text: str) -> Dict[str, Any]:
        """
        Extract metadata from text content.
        
        Args:
            text: Text content
            
        Returns:
            Dictionary containing extracted metadata
        """
        metadata = {
            'word_count': len(text.split()),
            'character_count': len(text),
            'sentence_count': len(re.findall(r'[.!?]+', text)),
            'paragraph_count': len([p for p in text.split('\n\n') if p.strip()]),
            'has_urls': bool(re.search(r'https?://\S+', text)),
            'has_emails': bool(re.search(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)),
            'has_phone_numbers': bool(re.search(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b', text)),
            'language_indicators': self._detect_language_indicators(text)
        }
        
        return metadata
    
    def _detect_language_indicators(self, text: str) -> Dict[str, float]:
        """
        Detect language indicators in text.
        
        Args:
            text: Text content
            
        Returns:
            Dictionary with language indicators
        """
        # Simple language detection based on common words
        english_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}
        spanish_words = {'el', 'la', 'y', 'o', 'pero', 'en', 'de', 'con', 'por', 'para', 'que'}
        french_words = {'le', 'la', 'et', 'ou', 'mais', 'dans', 'de', 'avec', 'par', 'pour', 'que'}
        
        words = set(text.lower().split())
        total_words = len(words)
        
        if total_words == 0:
            return {}
        
        indicators = {
            'english': len(words & english_words) / total_words,
            'spanish': len(words & spanish_words) / total_words,
            'french': len(words & french_words) / total_words
        }
        
        return indicators
    
    def chunk_text(self, text: str, preserve_sentences: bool = True) -> List[TextChunk]:
        """
        Split text into chunks with optional sentence preservation.
        
        Args:
            text: Text content to chunk
            preserve_sentences: Whether to preserve sentence boundaries
            
        Returns:
            List of TextChunk objects
        """
        if not text:
            return []
        
        chunks = []
        
        if preserve_sentences:
            chunks = self._chunk_by_sentences(text)
        else:
            chunks = self._chunk_by_characters(text)
        
        # Add metadata to chunks
        for i, chunk in enumerate(chunks):
            chunk.chunk_index = i
            chunk.metadata = self.extract_metadata(chunk.content)
        
        return chunks
    
    def _chunk_by_sentences(self, text: str) -> List[TextChunk]:
        """
        Chunk text by preserving sentence boundaries.
        
        Args:
            text: Text content
            
        Returns:
            List of TextChunk objects
        """
        # Split into sentences
        sentences = re.split(r'(?<=[.!?])\s+', text)
        
        chunks = []
        current_chunk = ""
        current_start = 0
        
        for sentence in sentences:
            # Check if adding this sentence would exceed chunk size
            if len(current_chunk) + len(sentence) > self.chunk_size and current_chunk:
                # Create chunk
                chunk_end = current_start + len(current_chunk)
                chunks.append(TextChunk(
                    content=current_chunk.strip(),
                    start_char=current_start,
                    end_char=chunk_end,
                    chunk_index=0,  # Will be set later
                    word_count=len(current_chunk.split()),
                    character_count=len(current_chunk)
                ))
                
                # Start new chunk with overlap
                overlap_text = self._get_overlap_text(current_chunk, self.chunk_overlap)
                current_chunk = overlap_text + sentence
                current_start = chunk_end - len(overlap_text)
            else:
                if current_chunk:
                    current_chunk += " " + sentence
                else:
                    current_chunk = sentence
        
        # Add final chunk
        if current_chunk:
            chunk_end = current_start + len(current_chunk)
            chunks.append(TextChunk(
                content=current_chunk.strip(),
                start_char=current_start,
                end_char=chunk_end,
                chunk_index=0,  # Will be set later
                word_count=len(current_chunk.split()),
                character_count=len(current_chunk)
            ))
        
        return chunks
    
    def _chunk_by_characters(self, text: str) -> List[TextChunk]:
        """
        Chunk text by character count.
        
        Args:
            text: Text content
            
        Returns:
            List of TextChunk objects
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = min(start + self.chunk_size, len(text))
            
            # Try to break at word boundary if not at end of text
            if end < len(text):
                # Look for last space within reasonable distance
                last_space = text.rfind(' ', start, end)
                if last_space > start + self.chunk_size * 0.8:  # At least 80% of chunk size
                    end = last_space
            
            chunk_content = text[start:end].strip()
            
            if chunk_content:
                chunks.append(TextChunk(
                    content=chunk_content,
                    start_char=start,
                    end_char=end,
                    chunk_index=0,  # Will be set later
                    word_count=len(chunk_content.split()),
                    character_count=len(chunk_content)
                ))
            
            # Move start position with overlap
            start = max(start + self.chunk_size - self.chunk_overlap, end)
        
        return chunks
    
    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """
        Get overlap text from the end of a chunk.
        
        Args:
            text: Source text
            overlap_size: Size of overlap in characters
            
        Returns:
            Overlap text
        """
        if len(text) <= overlap_size:
            return text
        
        overlap_text = text[-overlap_size:]
        
        # Try to start at word boundary
        first_space = overlap_text.find(' ')
        if first_space > 0:
            overlap_text = overlap_text[first_space + 1:]
        
        return overlap_text
    
    @staticmethod
    def calculate_content_hash(content: str) -> str:
        """
        Calculate SHA-256 hash of content.
        
        Args:
            content: Text content
            
        Returns:
            Hexadecimal hash string
        """
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    def extract_keywords(text: str, max_keywords: int = 10) -> List[str]:
        """
        Extract keywords from text using simple frequency analysis.
        
        Args:
            text: Text content
            max_keywords: Maximum number of keywords to return
            
        Returns:
            List of keywords
        """
        # Simple keyword extraction
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text.lower())
        
        # Remove common stop words
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'this', 'that', 'these', 'those',
            'was', 'were', 'been', 'have', 'has', 'had', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'shall'
        }
        
        words = [word for word in words if word not in stop_words]
        
        # Count frequency
        word_freq = {}
        for word in words:
            word_freq[word] = word_freq.get(word, 0) + 1
        
        # Sort by frequency and return top keywords
        keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in keywords[:max_keywords]]
