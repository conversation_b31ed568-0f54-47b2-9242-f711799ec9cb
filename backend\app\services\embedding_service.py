"""
Embedding service for generating and managing text embeddings.
"""

import json
import time
from typing import List, Dict, Any, Optional, Tuple
import logging
import numpy as np

# Import embedding libraries
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False
    logging.warning("sentence-transformers not available. Local embedding generation will be disabled.")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("openai not available. OpenAI embedding generation will be disabled.")

from core.config import settings

logger = logging.getLogger(__name__)


class EmbeddingService:
    """Service for generating and managing text embeddings."""
    
    def __init__(self):
        """Initialize embedding service."""
        self.model_name = settings.embedding_model
        self.dimension = settings.embedding_dimension
        self.openai_api_key = settings.openai_api_key
        
        # Initialize local model if available
        self._local_model = None
        if SENTENCE_TRANSFORMERS_AVAILABLE:
            try:
                self._local_model = SentenceTransformer(self.model_name)
                self.dimension = self._local_model.get_sentence_embedding_dimension()
                logger.info(f"Loaded local embedding model: {self.model_name}")
            except Exception as e:
                logger.error(f"Error loading local embedding model: {e}")
                self._local_model = None
        
        # Initialize OpenAI client if available
        self._openai_client = None
        if OPENAI_AVAILABLE and self.openai_api_key:
            try:
                openai.api_key = self.openai_api_key
                self._openai_client = openai
                logger.info("OpenAI client initialized")
            except Exception as e:
                logger.error(f"Error initializing OpenAI client: {e}")
    
    async def generate_embeddings(
        self, 
        texts: List[str], 
        model: Optional[str] = None,
        normalize: bool = True
    ) -> Tuple[List[List[float]], Dict[str, Any]]:
        """
        Generate embeddings for a list of texts.
        
        Args:
            texts: List of text strings to embed
            model: Optional model name to use (overrides default)
            normalize: Whether to normalize embeddings
            
        Returns:
            Tuple of (embeddings list, metadata dict)
        """
        if not texts:
            return [], {}
        
        start_time = time.time()
        model_used = model or self.model_name
        
        try:
            # Use OpenAI embeddings if model starts with 'text-embedding'
            if model_used.startswith('text-embedding') and self._openai_client:
                embeddings, metadata = await self._generate_openai_embeddings(texts, model_used)
            else:
                # Use local model
                embeddings, metadata = await self._generate_local_embeddings(texts, model_used, normalize)
            
            processing_time = (time.time() - start_time) * 1000
            
            metadata.update({
                'processing_time_ms': processing_time,
                'text_count': len(texts),
                'model_used': model_used,
                'normalized': normalize
            })
            
            logger.info(f"Generated {len(embeddings)} embeddings in {processing_time:.2f}ms")
            return embeddings, metadata
            
        except Exception as e:
            logger.error(f"Error generating embeddings: {e}")
            raise
    
    async def _generate_local_embeddings(
        self, 
        texts: List[str], 
        model: str,
        normalize: bool
    ) -> Tuple[List[List[float]], Dict[str, Any]]:
        """Generate embeddings using local sentence transformer model."""
        if not self._local_model:
            raise RuntimeError("Local embedding model not available")
        
        try:
            # Generate embeddings
            embeddings = self._local_model.encode(
                texts,
                normalize_embeddings=normalize,
                show_progress_bar=False
            )
            
            # Convert to list format
            embeddings_list = embeddings.tolist()
            
            metadata = {
                'model_type': 'sentence_transformer',
                'dimension': len(embeddings_list[0]) if embeddings_list else 0,
                'local_model': True
            }
            
            return embeddings_list, metadata
            
        except Exception as e:
            logger.error(f"Error with local embedding generation: {e}")
            raise
    
    async def _generate_openai_embeddings(
        self, 
        texts: List[str], 
        model: str
    ) -> Tuple[List[List[float]], Dict[str, Any]]:
        """Generate embeddings using OpenAI API."""
        if not self._openai_client:
            raise RuntimeError("OpenAI client not available")
        
        try:
            # OpenAI has a limit on batch size, so we might need to chunk
            batch_size = 100
            all_embeddings = []
            
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                
                response = await self._openai_client.Embedding.acreate(
                    input=batch_texts,
                    model=model
                )
                
                batch_embeddings = [item['embedding'] for item in response['data']]
                all_embeddings.extend(batch_embeddings)
            
            metadata = {
                'model_type': 'openai',
                'dimension': len(all_embeddings[0]) if all_embeddings else 0,
                'local_model': False,
                'api_usage': response.get('usage', {})
            }
            
            return all_embeddings, metadata
            
        except Exception as e:
            logger.error(f"Error with OpenAI embedding generation: {e}")
            raise
    
    def calculate_similarity(
        self, 
        embedding1: List[float], 
        embedding2: List[float],
        method: str = 'cosine'
    ) -> float:
        """
        Calculate similarity between two embeddings.
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            method: Similarity method ('cosine', 'euclidean', 'dot_product')
            
        Returns:
            Similarity score
        """
        try:
            vec1 = np.array(embedding1)
            vec2 = np.array(embedding2)
            
            if method == 'cosine':
                # Cosine similarity
                dot_product = np.dot(vec1, vec2)
                norm1 = np.linalg.norm(vec1)
                norm2 = np.linalg.norm(vec2)
                
                if norm1 == 0 or norm2 == 0:
                    return 0.0
                
                return float(dot_product / (norm1 * norm2))
            
            elif method == 'euclidean':
                # Euclidean distance (converted to similarity)
                distance = np.linalg.norm(vec1 - vec2)
                return float(1 / (1 + distance))
            
            elif method == 'dot_product':
                # Dot product similarity
                return float(np.dot(vec1, vec2))
            
            else:
                raise ValueError(f"Unknown similarity method: {method}")
                
        except Exception as e:
            logger.error(f"Error calculating similarity: {e}")
            return 0.0
    
    def find_most_similar(
        self, 
        query_embedding: List[float], 
        candidate_embeddings: List[List[float]],
        top_k: int = 5,
        threshold: float = 0.0
    ) -> List[Tuple[int, float]]:
        """
        Find most similar embeddings to a query embedding.
        
        Args:
            query_embedding: Query embedding vector
            candidate_embeddings: List of candidate embedding vectors
            top_k: Number of top results to return
            threshold: Minimum similarity threshold
            
        Returns:
            List of (index, similarity_score) tuples
        """
        if not candidate_embeddings:
            return []
        
        similarities = []
        
        for i, candidate in enumerate(candidate_embeddings):
            similarity = self.calculate_similarity(query_embedding, candidate)
            if similarity >= threshold:
                similarities.append((i, similarity))
        
        # Sort by similarity score (descending) and return top_k
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def serialize_embedding(self, embedding: List[float]) -> str:
        """
        Serialize embedding vector to JSON string for database storage.
        
        Args:
            embedding: Embedding vector
            
        Returns:
            JSON string representation
        """
        return json.dumps(embedding)
    
    def deserialize_embedding(self, embedding_json: str) -> List[float]:
        """
        Deserialize embedding vector from JSON string.
        
        Args:
            embedding_json: JSON string representation
            
        Returns:
            Embedding vector
        """
        return json.loads(embedding_json)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the current embedding model.
        
        Returns:
            Dictionary with model information
        """
        info = {
            'model_name': self.model_name,
            'dimension': self.dimension,
            'local_model_available': self._local_model is not None,
            'openai_available': self._openai_client is not None,
            'sentence_transformers_available': SENTENCE_TRANSFORMERS_AVAILABLE,
            'openai_library_available': OPENAI_AVAILABLE
        }
        
        if self._local_model:
            info['local_model_info'] = {
                'max_seq_length': getattr(self._local_model, 'max_seq_length', None),
                'device': str(getattr(self._local_model, 'device', None))
            }
        
        return info
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on embedding service.
        
        Returns:
            Health check results
        """
        health = {
            'status': 'healthy',
            'model_loaded': False,
            'api_available': False,
            'test_embedding_success': False
        }
        
        try:
            # Check if model is loaded
            if self._local_model:
                health['model_loaded'] = True
            
            # Check if OpenAI API is available
            if self._openai_client and self.openai_api_key:
                health['api_available'] = True
            
            # Test embedding generation
            test_texts = ["This is a test sentence."]
            embeddings, _ = await self.generate_embeddings(test_texts)
            
            if embeddings and len(embeddings) == 1:
                health['test_embedding_success'] = True
            
        except Exception as e:
            health['status'] = 'unhealthy'
            health['error'] = str(e)
            logger.error(f"Embedding service health check failed: {e}")
        
        return health
