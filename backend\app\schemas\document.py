"""
Pydantic schemas for document-related API requests and responses.
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator


class DocumentBase(BaseModel):
    """Base document schema with common fields."""
    title: Optional[str] = Field(None, max_length=500, description="Document title")
    description: Optional[str] = Field(None, description="Document description")
    author: Optional[str] = Field(None, max_length=255, description="Document author")
    language: str = Field("en", max_length=10, description="Document language code")
    tags: Optional[List[str]] = Field(None, description="Document tags")


class DocumentCreate(DocumentBase):
    """Schema for creating a new document."""
    pass


class DocumentUpdate(DocumentBase):
    """Schema for updating an existing document."""
    pass


class DocumentResponse(DocumentBase):
    """Schema for document response."""
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    mime_type: Optional[str]
    content_hash: Optional[str]
    is_processed: bool
    processing_status: str
    processing_error: Optional[str]
    word_count: int
    character_count: int
    chunk_count: int
    created_at: datetime
    updated_at: datetime
    processed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class DocumentListResponse(BaseModel):
    """Schema for paginated document list response."""
    documents: List[DocumentResponse]
    total: int
    page: int
    size: int
    pages: int


class DocumentChunkBase(BaseModel):
    """Base document chunk schema."""
    content: str = Field(..., min_length=1, description="Chunk content")
    chunk_index: int = Field(..., ge=0, description="Chunk order within document")
    start_char: Optional[int] = Field(None, ge=0, description="Start character position")
    end_char: Optional[int] = Field(None, ge=0, description="End character position")


class DocumentChunkCreate(DocumentChunkBase):
    """Schema for creating a new document chunk."""
    document_id: int = Field(..., gt=0, description="Document ID")


class DocumentChunkResponse(DocumentChunkBase):
    """Schema for document chunk response."""
    id: int
    document_id: int
    content_hash: str
    word_count: int
    character_count: int
    has_embedding: bool
    embedding_model: Optional[str]
    embedding_dimension: Optional[int]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class DocumentChunkWithSimilarity(DocumentChunkResponse):
    """Schema for document chunk with similarity score."""
    similarity_score: float = Field(..., ge=0.0, le=1.0, description="Similarity score")
    document_title: Optional[str] = Field(None, description="Document title")
    document_filename: str = Field(..., description="Document filename")


class DocumentProcessingStatus(BaseModel):
    """Schema for document processing status."""
    document_id: int
    status: str
    progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="Processing progress (0-1)")
    message: Optional[str] = Field(None, description="Status message")
    error: Optional[str] = Field(None, description="Error message if failed")


class DocumentStats(BaseModel):
    """Schema for document statistics."""
    total_documents: int
    processed_documents: int
    pending_documents: int
    failed_documents: int
    total_chunks: int
    total_embeddings: int
    total_file_size: int
    average_chunk_size: float
    supported_file_types: List[str]


class FileUploadResponse(BaseModel):
    """Schema for file upload response."""
    document_id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    status: str
    message: str


class BulkUploadResponse(BaseModel):
    """Schema for bulk file upload response."""
    successful_uploads: List[FileUploadResponse]
    failed_uploads: List[Dict[str, Any]]
    total_files: int
    successful_count: int
    failed_count: int


# Validation helpers
class DocumentFilters(BaseModel):
    """Schema for document filtering and search."""
    search: Optional[str] = Field(None, description="Search in title, description, or content")
    file_type: Optional[str] = Field(None, description="Filter by file type")
    author: Optional[str] = Field(None, description="Filter by author")
    language: Optional[str] = Field(None, description="Filter by language")
    tags: Optional[List[str]] = Field(None, description="Filter by tags")
    is_processed: Optional[bool] = Field(None, description="Filter by processing status")
    date_from: Optional[datetime] = Field(None, description="Filter documents created after this date")
    date_to: Optional[datetime] = Field(None, description="Filter documents created before this date")
    
    @validator('file_type')
    def validate_file_type(cls, v):
        if v is not None:
            allowed_types = ['pdf', 'txt', 'docx', 'md']
            if v.lower() not in allowed_types:
                raise ValueError(f'File type must be one of: {", ".join(allowed_types)}')
        return v.lower() if v else v


class PaginationParams(BaseModel):
    """Schema for pagination parameters."""
    page: int = Field(1, ge=1, description="Page number")
    size: int = Field(20, ge=1, le=100, description="Items per page")
    sort_by: str = Field("created_at", description="Sort field")
    sort_order: str = Field("desc", regex="^(asc|desc)$", description="Sort order")
