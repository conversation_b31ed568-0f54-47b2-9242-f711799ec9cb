2025-05-31 14:49:45 - core.logging_config - INFO - logging_config - setup_logging - 87 - Logging configured - Level: info, Format: text
2025-05-31 14:49:46 - core.logging_config - INFO - logging_config - setup_logging - 87 - Logging configured - Level: info, Format: text
2025-05-31 15:05:54 - core.logging_config - INFO - logging_config - setup_logging - 87 - Logging configured - Level: info, Format: text
2025-05-31 15:05:54 - uvicorn.error - INFO - server - _serve - 83 - Started server process [12764]
2025-05-31 15:05:54 - uvicorn.error - INFO - on - startup - 48 - Waiting for application startup.
2025-05-31 15:05:54 - main - INFO - main - lifespan - 31 - Starting Reality 2.0 v1.0.0
2025-05-31 15:05:54 - main - INFO - main - lifespan - 32 - Environment: development
2025-05-31 15:05:54 - sqlalchemy.engine.Engine - INFO - base - _connection_begin_impl - 2698 - B<PERSON>IN (implicit)
2025-05-31 15:05:54 - sqlalchemy.engine.Engine - INFO - base - _connection_commit_impl - 2704 - COMMIT
2025-05-31 15:05:54 - uvicorn.error - INFO - on - startup - 62 - Application startup complete.
2025-05-31 15:06:37 - core.logging_config - INFO - logging_config - setup_logging - 87 - Logging configured - Level: info, Format: text
2025-05-31 15:06:38 - core.logging_config - INFO - logging_config - setup_logging - 87 - Logging configured - Level: info, Format: text
