"""
Setup script for RAG server.
This script helps with initial setup and configuration.
"""

import os
import sys
import subprocess
import asyncio
from pathlib import Path


def print_header(title):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)


def print_step(step, description):
    """Print a formatted step."""
    print(f"\n[{step}] {description}")


def check_python_version():
    """Check if Python version is compatible."""
    print_step("1", "Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_dependencies():
    """Install required dependencies."""
    print_step("2", "Installing dependencies...")
    
    try:
        # Check if requirements.txt exists
        if not Path("requirements.txt").exists():
            print("❌ requirements.txt not found")
            return False
        
        # Install dependencies
        print("Installing packages from requirements.txt...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print(f"❌ Error installing dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def create_directories():
    """Create necessary directories."""
    print_step("3", "Creating directories...")
    
    directories = [
        "uploads",
        "vector_store", 
        "logs"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"✅ Created directory: {directory}")
        except Exception as e:
            print(f"❌ Error creating directory {directory}: {e}")
            return False
    
    return True


def create_env_file():
    """Create .env file if it doesn't exist."""
    print_step("4", "Setting up environment configuration...")
    
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    env_content = """# RAG Server Configuration

# Application Settings
APP_NAME="RAG Server"
APP_VERSION="1.0.0"
APP_DESCRIPTION="High-End RAG Server for Document Processing and AI"
APP_ENVIRONMENT="development"

# Server Configuration
BACKEND_HOST="0.0.0.0"
BACKEND_PORT=8000
BACKEND_RELOAD=true
BACKEND_LOG_LEVEL="info"

# Database Configuration
DATABASE_URL="sqlite:///./reality.db"
DATABASE_ECHO=false

# Security Settings
SECRET_KEY="your-super-secret-key-change-this-in-production"
ALGORITHM="HS256"

# CORS Configuration
CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"

# RAG Configuration
EMBEDDING_MODEL="all-MiniLM-L6-v2"
EMBEDDING_DIMENSION=384
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_RETRIEVAL_RESULTS=5
SIMILARITY_THRESHOLD=0.7

# File Upload Configuration
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="pdf,txt,docx,md"

# Vector Store Configuration
VECTOR_STORE_TYPE="faiss"
VECTOR_STORE_PATH="./vector_store"

# LLM Configuration (Optional - requires OpenAI API key)
# OPENAI_API_KEY="your-openai-api-key-here"
LLM_MODEL="gpt-3.5-turbo"
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=1000

# Logging Configuration
LOG_LEVEL="info"
LOG_FORMAT="text"
LOG_FILE_PATH="./logs/app.log"
"""
    
    try:
        with open(env_file, "w") as f:
            f.write(env_content)
        print("✅ Created .env file with default configuration")
        print("📝 Please edit .env file to customize your settings")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False


async def test_setup():
    """Test the setup by running basic checks."""
    print_step("5", "Testing setup...")
    
    try:
        # Add app to path
        sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))
        
        # Test imports
        print("Testing imports...")
        from core.config import settings
        from core.database import create_tables
        from services.embedding_service import EmbeddingService
        
        print("✅ All imports successful")
        
        # Test configuration
        print(f"✅ App name: {settings.app_name}")
        print(f"✅ Environment: {settings.app_environment}")
        print(f"✅ Database URL: {settings.database_url}")
        
        # Test database setup
        print("Testing database setup...")
        await create_tables()
        print("✅ Database tables created")
        
        # Test embedding service
        print("Testing embedding service...")
        embedding_service = EmbeddingService()
        model_info = embedding_service.get_model_info()
        print(f"✅ Embedding model: {model_info['model_name']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Setup test failed: {e}")
        return False


def print_next_steps():
    """Print next steps for the user."""
    print_header("Setup Complete!")
    
    print("""
🎉 RAG Server setup completed successfully!

Next steps:

1. 📝 Edit the .env file to customize your configuration:
   - Add your OpenAI API key (optional but recommended)
   - Adjust chunk sizes and similarity thresholds
   - Configure database settings for production

2. 🚀 Start the server:
   python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

3. 📖 Access the API documentation:
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

4. 🧪 Test the server:
   python test_rag_server.py

5. 📄 Upload your first document:
   curl -X POST "http://localhost:8000/api/v1/documents/upload" \\
     -H "Content-Type: multipart/form-data" \\
     -F "file=@your_document.pdf"

6. 🔍 Try a search query:
   curl -X GET "http://localhost:8000/api/v1/rag/quick-search?q=your_question"

For more information, see RAG_SERVER_README.md

Happy querying! 🤖
""")


async def main():
    """Main setup function."""
    print_header("RAG Server Setup")
    print("This script will help you set up the RAG server.")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Create directories
    if not create_directories():
        return False
    
    # Create .env file
    if not create_env_file():
        return False
    
    # Test setup
    if not await test_setup():
        print("\n⚠️  Setup completed with warnings. Check the errors above.")
        return False
    
    # Print next steps
    print_next_steps()
    return True


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        if success:
            sys.exit(0)
        else:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Setup failed with error: {e}")
        sys.exit(1)
