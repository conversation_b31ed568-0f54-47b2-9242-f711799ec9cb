# RAG Server Documentation

## Overview

This RAG (Retrieval-Augmented Generation) server provides a comprehensive solution for document processing, semantic search, and AI-powered question answering. Built with FastAPI, it offers a robust API for uploading documents, generating embeddings, and performing intelligent queries.

## Features

### 🔍 Document Processing
- **Multi-format Support**: PDF, DOCX, TXT, Markdown
- **Automatic Text Extraction**: Intelligent content extraction from various file formats
- **Smart Chunking**: Configurable text chunking with sentence boundary preservation
- **Metadata Extraction**: Automatic extraction of document metadata and statistics

### 🧠 Embedding & Vector Search
- **Multiple Embedding Models**: Support for Sentence Transformers and OpenAI embeddings
- **Vector Storage**: FAISS-based vector store for fast similarity search
- **Semantic Search**: Advanced semantic similarity search capabilities
- **Configurable Thresholds**: Adjustable similarity thresholds and result limits

### 🤖 AI-Powered Generation
- **RAG Pipeline**: Complete retrieval-augmented generation workflow
- **OpenAI Integration**: Support for GPT models for response generation
- **Conversational AI**: Context-aware chat functionality
- **Customizable Prompts**: Flexible system prompts and response formatting

### 📊 Management & Monitoring
- **Real-time Processing**: Asynchronous document processing
- **Health Monitoring**: Comprehensive health checks for all components
- **Statistics & Analytics**: Detailed statistics and performance metrics
- **Error Handling**: Robust error handling and logging

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │    │   Document      │    │   RAG Service   │
│                 │    │   Service       │    │                 │
│ • REST API      │────│ • File Upload   │────│ • Query Proc.   │
│ • Validation    │    │ • Text Extract  │    │ • Generation    │
│ • Error Handle  │    │ • Chunking      │    │ • Chat          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Database      │    │   Embedding     │    │   Vector Store  │
│                 │    │   Service       │    │                 │
│ • SQLAlchemy    │    │ • Transformers  │    │ • FAISS Index   │
│ • Documents     │    │ • OpenAI API    │    │ • Similarity    │
│ • Chunks        │    │ • Embeddings    │    │ • Search        │
│ • Embeddings    │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Installation

### Prerequisites
- Python 3.8+
- pip or conda
- Optional: OpenAI API key for advanced features

### Install Dependencies
```bash
cd backend
pip install -r requirements.txt
```

### Environment Configuration
Create a `.env` file in the backend directory:

```env
# Application Settings
APP_NAME="RAG Server"
APP_VERSION="1.0.0"
APP_ENVIRONMENT="development"

# Server Configuration
BACKEND_HOST="0.0.0.0"
BACKEND_PORT=8000

# Database
DATABASE_URL="sqlite:///./reality.db"

# RAG Configuration
EMBEDDING_MODEL="all-MiniLM-L6-v2"
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_RETRIEVAL_RESULTS=5
SIMILARITY_THRESHOLD=0.7

# OpenAI (Optional)
OPENAI_API_KEY="your-openai-api-key"
LLM_MODEL="gpt-3.5-turbo"

# File Upload
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES="pdf,txt,docx,md"

# Vector Store
VECTOR_STORE_TYPE="faiss"
VECTOR_STORE_PATH="./vector_store"
```

## Usage

### Starting the Server
```bash
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### API Documentation
Once running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Basic Workflow

#### 1. Upload Documents
```bash
curl -X POST "http://localhost:8000/api/v1/documents/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "title=My Document" \
  -F "author=John Doe"
```

#### 2. Search Documents
```bash
curl -X POST "http://localhost:8000/api/v1/rag/search" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "What is machine learning?",
    "max_results": 5,
    "similarity_threshold": 0.7
  }'
```

#### 3. Generate AI Response
```bash
curl -X POST "http://localhost:8000/api/v1/rag/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Explain machine learning concepts",
    "generate_response": true,
    "max_results": 3,
    "temperature": 0.7
  }'
```

## API Endpoints

### Document Management
- `POST /api/v1/documents/upload` - Upload single document
- `POST /api/v1/documents/upload/bulk` - Upload multiple documents
- `GET /api/v1/documents/` - List documents with filtering
- `GET /api/v1/documents/{id}` - Get document details
- `PUT /api/v1/documents/{id}` - Update document metadata
- `DELETE /api/v1/documents/{id}` - Delete document
- `GET /api/v1/documents/{id}/content` - Get extracted text
- `GET /api/v1/documents/{id}/chunks` - Get document chunks
- `GET /api/v1/documents/stats/overview` - Get statistics

### RAG Operations
- `POST /api/v1/rag/search` - Semantic search
- `POST /api/v1/rag/generate` - Generate AI response
- `POST /api/v1/rag/chat` - Conversational query
- `POST /api/v1/rag/embeddings` - Generate embeddings
- `GET /api/v1/rag/status` - System status
- `GET /api/v1/rag/health` - Health check

### Quick Access
- `GET /api/v1/rag/quick-search?q=query` - Simple search
- `GET /api/v1/rag/quick-generate?q=question` - Simple Q&A

## Configuration Options

### Embedding Models
- **Sentence Transformers**: Local models (all-MiniLM-L6-v2, all-mpnet-base-v2)
- **OpenAI**: text-embedding-ada-002, text-embedding-3-small

### Vector Stores
- **FAISS**: Fast similarity search (default)
- **Future**: Chroma, Pinecone support planned

### Text Processing
- **Chunk Size**: 500-2000 characters (default: 1000)
- **Overlap**: 100-500 characters (default: 200)
- **Sentence Preservation**: Maintains sentence boundaries

## Testing

### Run Test Suite
```bash
cd backend
python test_rag_server.py
```

### Manual Testing
```bash
# Test health
curl http://localhost:8000/api/v1/rag/health

# Test quick search
curl "http://localhost:8000/api/v1/rag/quick-search?q=test"
```

## Performance Considerations

### Optimization Tips
1. **Embedding Model**: Choose appropriate model for your use case
2. **Chunk Size**: Optimize based on document types
3. **Vector Store**: Consider upgrading to Pinecone for large datasets
4. **Database**: Use PostgreSQL for production environments

### Scaling
- **Horizontal**: Deploy multiple instances behind load balancer
- **Vertical**: Increase memory for larger vector stores
- **Database**: Use connection pooling and read replicas

## Troubleshooting

### Common Issues

#### Dependencies Not Found
```bash
pip install -r requirements.txt
```

#### Database Connection Error
Check DATABASE_URL in environment variables

#### Embedding Model Loading Error
Ensure sufficient memory and internet connection for model download

#### OpenAI API Error
Verify OPENAI_API_KEY is set correctly

### Logs
Check application logs in `./logs/app.log` for detailed error information.

## Development

### Project Structure
```
backend/
├── app/
│   ├── api/v1/endpoints/     # API endpoints
│   ├── core/                 # Configuration & database
│   ├── models/               # SQLAlchemy models
│   ├── schemas/              # Pydantic schemas
│   ├── services/             # Business logic
│   ├── crud/                 # Database operations
│   └── utils/                # Utilities
├── alembic/                  # Database migrations
├── tests/                    # Test suite
└── uploads/                  # File storage
```

### Adding New Features
1. Define schemas in `schemas/`
2. Create models in `models/`
3. Implement services in `services/`
4. Add CRUD operations in `crud/`
5. Create API endpoints in `api/v1/endpoints/`
6. Update router in `api/v1/api.py`

## License

This project is part of the Reality 2.0 application suite.
