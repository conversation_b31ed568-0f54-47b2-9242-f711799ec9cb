"""
RAG (Retrieval-Augmented Generation) service for query processing and response generation.
"""

import time
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import logging

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from models.document import Document, DocumentChunk, ChunkEmbedding
from schemas.rag import (
    RAGQuery, RAGGenerationQuery, RAGChatQuery,
    RetrievalResult, RAGRetrievalResponse, RAGGenerationResponse, RAGChatResponse,
    GeneratedResponse, RAGChatMessage, QueryType, ResponseFormat
)
from schemas.document import DocumentChunkWithSimilarity
from services.embedding_service import EmbeddingService
from services.vector_store_service import VectorStoreService
from core.config import settings

# Import LLM libraries
try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    logging.warning("openai not available. Text generation will be disabled.")

logger = logging.getLogger(__name__)


class RAGService:
    """Service for RAG query processing and response generation."""
    
    def __init__(self):
        """Initialize RAG service."""
        self.embedding_service = EmbeddingService()
        self.vector_store = VectorStoreService()
        
        # Initialize OpenAI client if available
        self.openai_client = None
        if OPENAI_AVAILABLE and settings.openai_api_key:
            openai.api_key = settings.openai_api_key
            self.openai_client = openai
    
    async def retrieve_relevant_chunks(
        self, 
        db: Session,
        query: RAGQuery
    ) -> RAGRetrievalResponse:
        """
        Retrieve relevant document chunks for a query.
        
        Args:
            db: Database session
            query: RAG query parameters
            
        Returns:
            Retrieval response with relevant chunks
        """
        start_time = time.time()
        
        try:
            # Generate query embedding
            query_embeddings, _ = await self.embedding_service.generate_embeddings([query.query])
            query_embedding = query_embeddings[0]
            
            # Search vector store
            similar_chunks = await self.vector_store.search_similar(
                query_embedding,
                top_k=query.max_results * 2,  # Get more candidates for filtering
                threshold=query.similarity_threshold
            )
            
            if not similar_chunks:
                return RAGRetrievalResponse(
                    query=query.query,
                    query_type=query.query_type,
                    results=[],
                    total_results=0,
                    max_results=query.max_results,
                    similarity_threshold=query.similarity_threshold,
                    processing_time_ms=(time.time() - start_time) * 1000,
                    timestamp=datetime.utcnow()
                )
            
            # Get chunk IDs and scores
            chunk_ids = [chunk_id for chunk_id, _ in similar_chunks]
            similarity_scores = {chunk_id: score for chunk_id, score in similar_chunks}
            
            # Query database for chunk details with document info
            chunks_query = db.query(DocumentChunk, Document).join(
                Document, DocumentChunk.document_id == Document.id
            ).filter(DocumentChunk.id.in_(chunk_ids))
            
            # Apply document filters
            if query.document_ids:
                chunks_query = chunks_query.filter(Document.id.in_(query.document_ids))
            
            if query.file_types:
                chunks_query = chunks_query.filter(Document.file_type.in_(query.file_types))
            
            if query.authors:
                author_filters = [Document.author.ilike(f"%{author}%") for author in query.authors]
                chunks_query = chunks_query.filter(or_(*author_filters))
            
            if query.tags:
                for tag in query.tags:
                    chunks_query = chunks_query.filter(Document.tags.ilike(f"%{tag}%"))
            
            if query.date_from:
                chunks_query = chunks_query.filter(Document.created_at >= query.date_from)
            
            if query.date_to:
                chunks_query = chunks_query.filter(Document.created_at <= query.date_to)
            
            # Execute query
            chunk_results = chunks_query.all()
            
            # Create retrieval results
            results = []
            for rank, (chunk, document) in enumerate(chunk_results[:query.max_results], 1):
                similarity_score = similarity_scores.get(chunk.id, 0.0)
                
                # Create chunk with similarity
                chunk_with_similarity = DocumentChunkWithSimilarity(
                    id=chunk.id,
                    document_id=chunk.document_id,
                    content=chunk.content,
                    content_hash=chunk.content_hash,
                    chunk_index=chunk.chunk_index,
                    start_char=chunk.start_char,
                    end_char=chunk.end_char,
                    word_count=chunk.word_count,
                    character_count=chunk.character_count,
                    has_embedding=chunk.has_embedding,
                    embedding_model=chunk.embedding_model,
                    embedding_dimension=chunk.embedding_dimension,
                    created_at=chunk.created_at,
                    updated_at=chunk.updated_at,
                    similarity_score=similarity_score,
                    document_title=document.title,
                    document_filename=document.filename
                )
                
                result = RetrievalResult(
                    chunk=chunk_with_similarity,
                    relevance_score=similarity_score,
                    rank=rank
                )
                results.append(result)
            
            processing_time = (time.time() - start_time) * 1000
            
            return RAGRetrievalResponse(
                query=query.query,
                query_type=query.query_type,
                results=results,
                total_results=len(results),
                max_results=query.max_results,
                similarity_threshold=query.similarity_threshold,
                processing_time_ms=processing_time,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error retrieving relevant chunks: {e}")
            raise
    
    async def generate_response(
        self, 
        db: Session,
        query: RAGGenerationQuery
    ) -> RAGGenerationResponse:
        """
        Generate AI response using retrieved context.
        
        Args:
            db: Database session
            query: RAG generation query
            
        Returns:
            Generated response with retrieval context
        """
        start_time = time.time()
        
        try:
            # First, retrieve relevant chunks
            retrieval_query = RAGQuery(
                query=query.query,
                query_type=query.query_type,
                max_results=query.max_results,
                similarity_threshold=query.similarity_threshold,
                include_metadata=query.include_metadata,
                document_ids=query.document_ids,
                file_types=query.file_types,
                authors=query.authors,
                tags=query.tags,
                date_from=query.date_from,
                date_to=query.date_to
            )
            
            retrieval_response = await self.retrieve_relevant_chunks(db, retrieval_query)
            
            # Generate response if requested and context is available
            generated_response = None
            if query.generate_response and retrieval_response.results:
                generated_response = await self._generate_llm_response(
                    query, retrieval_response.results
                )
            
            total_processing_time = (time.time() - start_time) * 1000
            
            return RAGGenerationResponse(
                query=query.query,
                retrieval=retrieval_response,
                generated_response=generated_response,
                total_processing_time_ms=total_processing_time,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error generating RAG response: {e}")
            raise
    
    async def _generate_llm_response(
        self, 
        query: RAGGenerationQuery,
        retrieval_results: List[RetrievalResult]
    ) -> GeneratedResponse:
        """
        Generate LLM response using retrieved context.
        
        Args:
            query: Generation query parameters
            retrieval_results: Retrieved context chunks
            
        Returns:
            Generated response
        """
        if not self.openai_client:
            raise RuntimeError("OpenAI client not available for text generation")
        
        try:
            # Prepare context from retrieved chunks
            context_parts = []
            sources = []
            
            for result in retrieval_results:
                chunk = result.chunk
                context_parts.append(f"[Source: {chunk.document_filename}]\n{chunk.content}")
                
                if query.include_sources:
                    source_info = {
                        'document': chunk.document_filename,
                        'title': chunk.document_title,
                        'chunk_index': chunk.chunk_index,
                        'similarity': chunk.similarity_score
                    }
                    sources.append(source_info)
            
            context = "\n\n".join(context_parts)
            
            # Prepare system prompt
            system_prompt = query.system_prompt or """You are a helpful AI assistant that answers questions based on the provided context. 
Use only the information from the context to answer the question. If the context doesn't contain enough information to answer the question, say so clearly.
Be accurate, concise, and cite the sources when possible."""
            
            # Prepare user prompt
            user_prompt = f"""Context:
{context}

Question: {query.query}

Please provide a comprehensive answer based on the context above."""
            
            # Generate response
            response = await self.openai_client.ChatCompletion.acreate(
                model=query.llm_model or settings.llm_model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=query.temperature,
                max_tokens=query.max_tokens
            )
            
            generated_content = response.choices[0].message.content
            
            # Add sources if requested
            if query.include_sources and sources:
                if query.response_format == ResponseFormat.MARKDOWN:
                    sources_text = "\n\n## Sources\n"
                    for i, source in enumerate(sources, 1):
                        sources_text += f"{i}. **{source['document']}** (similarity: {source['similarity']:.2f})\n"
                    generated_content += sources_text
                elif query.response_format == ResponseFormat.TEXT:
                    sources_text = "\n\nSources:\n"
                    for i, source in enumerate(sources, 1):
                        sources_text += f"{i}. {source['document']} (similarity: {source['similarity']:.2f})\n"
                    generated_content += sources_text
            
            return GeneratedResponse(
                content=generated_content,
                format=query.response_format,
                model_used=response.model,
                temperature=query.temperature,
                tokens_used=response.usage.total_tokens,
                sources_count=len(sources)
            )
            
        except Exception as e:
            logger.error(f"Error generating LLM response: {e}")
            raise
    
    async def chat_query(
        self, 
        db: Session,
        query: RAGChatQuery
    ) -> RAGChatResponse:
        """
        Process conversational RAG query.
        
        Args:
            db: Database session
            query: Chat query parameters
            
        Returns:
            Chat response with context
        """
        start_time = time.time()
        
        try:
            # Prepare enhanced query with conversation context
            enhanced_query = query.message
            
            if query.conversation_history:
                # Add recent conversation context
                recent_messages = query.conversation_history[-query.context_window:]
                context_parts = []
                
                for msg in recent_messages:
                    context_parts.append(f"{msg.role}: {msg.content}")
                
                conversation_context = "\n".join(context_parts)
                enhanced_query = f"Conversation context:\n{conversation_context}\n\nCurrent question: {query.message}"
            
            # Create RAG generation query
            rag_query = RAGGenerationQuery(
                query=enhanced_query,
                generate_response=True,
                response_format=ResponseFormat.TEXT,
                max_results=query.max_results,
                similarity_threshold=query.similarity_threshold,
                temperature=query.temperature,
                max_tokens=query.max_tokens,
                include_sources=True,
                document_ids=query.document_ids,
                file_types=query.file_types
            )
            
            # Generate response
            rag_response = await self.generate_response(db, rag_query)
            
            # Create chat message
            assistant_message = RAGChatMessage(
                role="assistant",
                content=rag_response.generated_response.content if rag_response.generated_response else "I couldn't find relevant information to answer your question.",
                timestamp=datetime.utcnow(),
                metadata={
                    'sources_count': len(rag_response.retrieval.results),
                    'model_used': rag_response.generated_response.model_used if rag_response.generated_response else None,
                    'tokens_used': rag_response.generated_response.tokens_used if rag_response.generated_response else 0
                }
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            return RAGChatResponse(
                message=assistant_message,
                retrieval_results=rag_response.retrieval.results,
                processing_time_ms=processing_time,
                timestamp=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Error processing chat query: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on RAG service.
        
        Returns:
            Health check results
        """
        health = {
            'status': 'healthy',
            'embedding_service': await self.embedding_service.health_check(),
            'vector_store': await self.vector_store.health_check(),
            'llm_available': self.openai_client is not None,
            'components_healthy': True
        }
        
        # Check if all components are healthy
        if (health['embedding_service']['status'] != 'healthy' or 
            health['vector_store']['status'] != 'healthy'):
            health['status'] = 'unhealthy'
            health['components_healthy'] = False
        
        return health
